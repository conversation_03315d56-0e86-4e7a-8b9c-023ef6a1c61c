# 证书模板修复说明

## 修复的问题

### 1. 预览图与PDF生成位置不一致问题

**问题描述：**
- 预览组件使用CSS flexbox相对布局
- PDF生成器使用绝对坐标定位，Y坐标从底部开始计算
- 两者坐标系统不匹配，导致文本位置不一致

**解决方案：**
- 修改 `CertificatePreview.tsx` 组件，使用绝对定位匹配模板坐标
- 添加缩放因子计算，确保预览与PDF比例一致
- 修正Y坐标计算：`top = (containerHeight - y - height) * scaleFactor`
- 使用模板中定义的精确坐标进行定位

**修改文件：**
- `src/components/certificate/CertificatePreview.tsx`
- `src/lib/certificate-templates.ts` (调整completion-template-1坐标)

### 2. Dancing Script Regular字体未生效问题

**问题描述：**
- PDF生成器只支持标准字体（Helvetica, Times Roman, Courier）
- 模板中使用"Dancing Script Regular"，但实际字体名称是"Dancing Script"
- 缺少自定义字体加载机制

**解决方案：**
- 创建 `FontLoader` 类支持Google Fonts字体加载
- 修正模板中的字体名称从"Dancing Script Regular"到"Dancing Script"
- 在PDF生成器中添加字体映射和后备机制
- 支持动态下载和缓存Google Fonts字体文件

**新增文件：**
- `src/lib/font-loader.ts` - 字体加载器

**修改文件：**
- `src/lib/pdf-generator.ts` - 集成字体加载器
- `src/lib/certificate-templates.ts` - 修正字体名称

## 技术实现细节

### 坐标系统统一

```typescript
// 预览组件中的坐标计算
const scaleFactor = template.orientation === 'landscape' ? 0.5 : 0.6;
const containerHeight = template.orientation === 'landscape' ? 595 : 842;

// 使用模板坐标进行绝对定位，修正Y坐标系统差异
style={{
  left: `${template.layout.name.x * scaleFactor}px`,
  top: `${(containerHeight - template.layout.name.y - template.layout.name.height) * scaleFactor}px`,
  width: `${template.layout.name.width * scaleFactor}px`,
  height: `${template.layout.name.height * scaleFactor}px`,
}}
```

**关键修复：**
- PDF坐标系：Y轴从底部开始，向上为正
- 预览坐标系：Y轴从顶部开始，向下为正
- 转换公式：`previewY = containerHeight - pdfY - height`

### 字体加载机制

```typescript
// 字体加载器支持Google Fonts
const customFonts = await FontLoader.loadFonts(this.pdfDoc, [
  { family: 'Dancing Script', weight: 400 },
  { family: 'Playfair Display', weight: 600 },
  // ...
]);

// 后备字体映射
if (!this.fonts['Dancing Script']) {
  this.fonts['Dancing Script'] = this.fonts.timesRoman;
}
```

### completion-template-1坐标调整

```typescript
layout: {
  name: {
    x: 297.5,  // 居中对齐：595/2 = 297.5
    y: 350,    // 从底部350点位置
    width: 400,
    height: 40,
    align: 'center',
    fontSize: 28,
    fontFamily: 'Dancing Script', // 修正字体名称
    // ...
  },
  details: {
    x: 297.5,  // 居中对齐
    y: 450,    // 详情位置
    width: 450,
    height: 120,
    // ...
  },
  date: {
    x: 100,    // 左侧位置
    y: 700,    // 底部位置
    // ...
  },
  signature: {
    x: 495,    // 右侧位置
    y: 700,    // 底部位置
    // ...
  }
}
```

## 额外改进

### 1. **坐标调试功能**

新增了坐标可视化调试工具：
- 点击"显示坐标"按钮可以看到各字段的边界框
- 不同颜色标识不同字段：红色(姓名)、蓝色(详情)、绿色(日期)、紫色(签名)
- 帮助开发者精确调整文本位置

### 2. **坐标系统修正**

- **PDF坐标系：** Y轴从底部开始，向上为正
- **预览坐标系：** Y轴从顶部开始，向下为正
- **转换公式：** `previewY = containerHeight - pdfY - height`

### 3. **字体加载优化**

- 创建了FontLoader类支持Google Fonts
- 添加了字体缓存机制
- 实现了字体后备策略

## 测试验证

创建了测试页面 `/test-completion` 用于验证修复效果：

1. **位置一致性测试：**
   - 对比预览图和生成的PDF中文本位置
   - 验证各字段（姓名、详情、日期、签名）的对齐

2. **字体效果测试：**
   - 验证Dancing Script字体在PDF中的显示效果
   - 测试字体后备机制

## 使用说明

### 访问测试页面
```
http://localhost:3000/test-completion
```

### 验证步骤
1. 在测试页面输入证书信息
2. 观察实时预览效果
3. 点击"生成PDF"按钮
4. 对比预览图与生成的PDF文件

## 注意事项

1. **字体加载性能：**
   - 首次加载Google Fonts可能需要时间
   - 字体文件会被缓存以提高后续性能

2. **网络依赖：**
   - Google Fonts需要网络连接
   - 离线环境会自动使用后备字体

3. **浏览器兼容性：**
   - 字体加载依赖现代浏览器的fetch API
   - 旧版浏览器会自动降级到标准字体

## 后续优化建议

1. **本地字体文件：**
   - 将常用字体文件下载到 `public/fonts/` 目录
   - 减少网络依赖，提高加载速度

2. **字体预加载：**
   - 在应用启动时预加载常用字体
   - 使用Service Worker缓存字体文件

3. **更多字体支持：**
   - 扩展字体加载器支持更多字体格式
   - 添加字体预览功能

4. **坐标可视化工具：**
   - 开发坐标调试工具
   - 支持拖拽调整文本位置
