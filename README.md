# Certificate Maker - 专业证书生成器

一个基于 Next.js 的现代化证书生成平台，为用户提供专业、美观的证书模板和便捷的在线生成服务。

## 🚀 快速开始

```bash
# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

## ✨ 核心特性

- **🏆 多类别证书模板**：成就、完成、参与、优秀四大类别
- **🎨 专业设计**：每个分类配置专门的模板
- **👀 实时预览**：所见即所得的证书预览功能
- **📄 高质量输出**：生成高分辨率PDF文件
- **📱 响应式设计**：完美适配桌面端和移动端
- **🔍 SEO优化**：针对搜索引擎优化的页面结构

## 🛠️ 技术栈

- **前端框架**：Next.js 14 (App Router)
- **UI框架**：React 18 + Tailwind CSS + Shadcn/ui
- **PDF生成**：pdf-lib
- **类型系统**：TypeScript
- **包管理器**：pnpm
- **部署平台**：Vercel

## 📋 证书类别

### 🏆 Achievement Certificates (成就证书)
适用于员工表彰、学术奖励、业务成就等场景

### 📚 Completion Certificates (完成证书)
适用于课程完成、培训项目、教育成就等场景

### 🤝 Participation Certificates (参与证书)
适用于活动参与、研讨会、会议出席等场景

### ⭐ Excellence Certificates (优秀证书)
适用于卓越表现、杰出贡献、优秀奖励等场景

## 📖 详细文档

查看 [PROJECT_DOCUMENTATION.md](./PROJECT_DOCUMENTATION.md) 获取完整的项目文档，包括：

- 详细的技术架构说明
- 完整的功能介绍
- 开发和部署指南
- 性能优化策略
- SEO优化方案
- 故障排除指南

## 🚀 部署

项目已优化用于 Vercel 部署，也可以部署到任何支持 Next.js 的平台。

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。
