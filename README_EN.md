# Certificate Maker 🏆

A modern, mobile-first certificate generator built with Next.js 14, Tai<PERSON>windCSS, and Shadcn/ui. Create beautiful, professional certificates online for free with no registration required.

## ✨ Features

- 🎨 **4 Professional Templates** - Choose from elegant, pre-designed certificate templates
- 📱 **Mobile-First Design** - Optimized for all devices with touch-friendly interactions
- ⚡ **Real-time Preview** - See your certificate update as you type
- 🔍 **SEO Optimized** - Built with Google SEO best practices
- 🚀 **High Performance** - Optimized for Core Web Vitals
- 📄 **PDF Generation** - High-quality PDF download (coming in Phase 2)
- 🌐 **English-Only Interface** - Designed for English-speaking users
- ♿ **Accessible** - WCAG compliant design

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- Yarn or npm

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/certificate-maker.git
cd certificate-maker
```

2. Install dependencies:
```bash
yarn install
# or
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env.local
```

4. Start the development server:
```bash
yarn dev
# or
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 🏗️ Tech Stack

- **Framework**: Next.js 14 (App Router)
- **Styling**: TailwindCSS
- **UI Components**: Shadcn/ui
- **Language**: TypeScript
- **Form Handling**: React Hook Form + Zod
- **PDF Generation**: PDF-lib (Phase 2)
- **Analytics**: Google Analytics 4
- **Deployment**: Vercel

## 📁 Project Structure

```
certificate-maker/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes (sitemap, robots)
│   │   ├── globals.css        # Global styles
│   │   ├── layout.tsx         # Root layout
│   │   └── page.tsx           # Homepage
│   ├── components/            # React components
│   │   ├── ui/               # Base UI components
│   │   ├── certificate/      # Certificate-specific components
│   │   ├── layout/           # Layout components
│   │   └── common/           # Common components
│   ├── lib/                  # Utility libraries
│   │   ├── certificate-templates.ts  # Template definitions
│   │   ├── seo.ts            # SEO utilities
│   │   ├── analytics.ts      # Analytics tools
│   │   └── utils.ts          # Common utilities
│   ├── types/                # TypeScript type definitions
│   └── hooks/                # Custom React hooks
├── public/                   # Static assets
├── docs/                     # Project documentation
└── README.md
```

## 🎨 Certificate Templates

### 1. Classic Business Style
- **Theme**: Professional blue design
- **Best for**: Corporate certificates, business awards
- **Colors**: Blue (#1E40AF) and white
- **Fonts**: Playfair Display, Inter, Dancing Script

### 2. Modern Minimalist Style
- **Theme**: Clean black and white design
- **Best for**: Modern certificates, tech awards
- **Colors**: Black (#000000) and gray
- **Fonts**: Inter family

### 3. Elegant Green Style
- **Theme**: Nature-inspired green design
- **Best for**: Environmental awards, education certificates
- **Colors**: Green (#059669) and light green
- **Fonts**: Crimson Text, Source Sans Pro, Great Vibes

### 4. Traditional Formal Style
- **Theme**: Classic brown and cream design
- **Best for**: Academic certificates, formal awards
- **Colors**: Brown (#92400E) and cream
- **Fonts**: Libre Baskerville, Allura

## 🔧 Development

### Available Scripts

```bash
# Development
yarn dev          # Start development server
yarn build        # Build for production
yarn start        # Start production server
yarn lint         # Run ESLint
yarn type-check   # Run TypeScript check
```

### Code Quality

- **TypeScript**: Strict mode enabled
- **ESLint**: Code linting with Next.js config
- **Prettier**: Code formatting
- **Husky**: Git hooks (optional)

### Testing

```bash
# Run template validation tests
yarn test:templates

# Run performance tests
yarn test:performance
```

## 📊 Performance

### Core Web Vitals Targets
- **LCP**: < 2.5s
- **FID**: < 100ms
- **CLS**: < 0.1
- **Lighthouse Score**: > 90

### SEO Features
- Semantic HTML structure
- Optimized meta tags
- Structured data (Schema.org)
- XML sitemap generation
- Mobile-friendly design
- Fast loading speeds

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
# Build the application
yarn build

# Start production server
yarn start
```

## 🔒 Environment Variables

Copy `.env.example` to `.env.local` and configure:

```env
NEXT_PUBLIC_BASE_URL=https://your-domain.com
NEXT_PUBLIC_GA_ID=G-XXXXXXXXXX
GOOGLE_VERIFICATION_CODE=your-verification-code
```

## 📈 Analytics

The application includes comprehensive analytics:

- **Google Analytics 4**: User behavior tracking
- **Core Web Vitals**: Performance monitoring
- **Custom Events**: Certificate generation tracking
- **Error Tracking**: Error monitoring and reporting

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [TailwindCSS](https://tailwindcss.com/) - CSS framework
- [Shadcn/ui](https://ui.shadcn.com/) - UI components
- [PDF-lib](https://pdf-lib.js.org/) - PDF generation
- [Lucide React](https://lucide.dev/) - Icons

## 📞 Support

If you have any questions or need help, please:

1. Check the [documentation](./docs/)
2. Search existing [issues](https://github.com/your-username/certificate-maker/issues)
3. Create a new issue if needed

---

Made with ❤️ for educators and professionals worldwide.
