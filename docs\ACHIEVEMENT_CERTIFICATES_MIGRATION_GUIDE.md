# Achievement Certificates 页面迁移指南

## 📋 概述

本文档详细说明了 `/achievement-certificates` 页面的完整实现逻辑，包括路由结构、组件架构、表单处理、PDF生成流程等，便于将此功能迁移到其他项目。

## 🗂️ 路由结构

### URL 映射关系
```
/certificate-templates/achievement-certificates/
├── 路由文件: src/app/certificate-templates/[category]/page.tsx
├── 动态参数: { category: "achievement-certificates" }
├── 分类枚举: CertificateCategory.ACHIEVEMENT
└── 模板数据: TemplateManager.getTemplatesByCategory()
```

### 路由实现
```typescript
// src/app/certificate-templates/[category]/page.tsx

interface CategoryDetailPageProps {
  params: {
    category: string; // "achievement-certificates"
  };
}

export default function CategoryDetailPageRoute({ params }: CategoryDetailPageProps) {
  // 1. 根据URL slug获取分类配置
  const categoryConfig = TemplateManager.getCategoryConfigBySlug(params.category);
  
  // 2. 获取该分类的所有模板
  const categoryEnum = TemplateManager.getCategoryBySlug(params.category);
  const templates = TemplateManager.getTemplatesByCategory(categoryEnum);
  
  // 3. 选择默认模板
  const defaultTemplate = templates[0];
  
  // 4. 渲染页面组件
  return (
    <PageLayout>
      <CategoryDetailPage
        category={categoryConfig}
        templates={templates}
        defaultTemplate={defaultTemplate}
      />
    </PageLayout>
  );
}
```

## 🏗️ 组件架构

### 主要组件层次结构
```
CategoryDetailPage (页面容器)
├── BreadcrumbNavigation (面包屑导航)
├── 页面头部 (标题、描述、特性展示)
├── 模板选择器 (VisualTemplateCard 网格)
└── CertificateMaker (证书生成器)
    ├── SimpleCertificateForm (表单组件)
    ├── CertificatePreview (预览组件)
    └── PDF生成按钮
```

### 核心组件详解

#### 1. CategoryDetailPage 组件
```typescript
// src/components/certificate/CategoryDetailPage.tsx

interface CategoryDetailPageProps {
  category: TemplateCategory;      // 分类配置信息
  templates: CertificateTemplate[]; // 该分类的所有模板
  defaultTemplate: CertificateTemplate | null; // 默认选中的模板
}

export default function CategoryDetailPage({
  category,
  templates,
  defaultTemplate
}: CategoryDetailPageProps) {
  // 状态管理
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(
    defaultTemplate
  );

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 面包屑导航 */}
      <BreadcrumbNavigation items={breadcrumbItems} />
      
      {/* 页面头部 */}
      <div className="mb-12">
        <h1>{categoryContent.title}</h1>
        <h2>{categoryContent.subtitle}</h2>
        <p>{categoryContent.description}</p>
      </div>

      {/* 模板选择网格 */}
      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
        {templates.map((template) => (
          <VisualTemplateCard
            key={template.id}
            template={template}
            isSelected={selectedTemplate?.id === template.id}
            onSelect={() => setSelectedTemplate(template)}
          />
        ))}
      </div>

      {/* 证书生成器 */}
      <div className="mb-16">
        {selectedTemplate && (
          <CertificateMaker
            selectedTemplate={selectedTemplate}
            templates={templates}
            onTemplateChange={setSelectedTemplate}
          />
        )}
      </div>
    </div>
  );
}
```

#### 2. CertificateMaker 组件 (核心生成器)
```typescript
// src/components/certificate/CertificateMaker.tsx

interface CertificateMakerProps {
  selectedTemplate?: CertificateTemplate;
  templates?: CertificateTemplate[];
  onTemplateChange?: (template: CertificateTemplate) => void;
}

export default function CertificateMaker({
  selectedTemplate: initialTemplate,
  templates = [],
  onTemplateChange
}: CertificateMakerProps) {
  // 状态管理
  const [formData, setFormData] = useState<CertificateData>({
    templateId: getDefaultTemplate().id,
    recipientName: '',    // 获奖者姓名
    date: '',            // 日期
    signature: '',       // 签名
    details: '',         // 详细信息/副标题
  });
  const [isGenerating, setIsGenerating] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  // 表单数据更新处理
  const handleFormDataChange = useCallback((data: Partial<CertificateData>) => {
    setFormData(prev => ({
      ...prev,
      ...data,
    }));
  }, []);

  // PDF生成处理
  const handleGeneratePDF = useCallback(async () => {
    // 1. 表单验证
    if (!selectedTemplate || !formData.recipientName || !formData.date || 
        !formData.signature || !formData.details) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields before generating the certificate.",
        variant: "destructive",
      });
      return;
    }

    // 2. 开始生成流程
    setIsGenerating(true);
    setGenerationProgress(0);

    try {
      // 3. 模拟进度更新
      const progressInterval = setInterval(() => {
        setGenerationProgress(prev => Math.min(prev + 10, 90));
      }, 100);

      // 4. 调用PDF生成器
      await generateCertificatePDF(selectedTemplate, formData);

      // 5. 完成处理
      clearInterval(progressInterval);
      setGenerationProgress(100);
      
      toast({
        title: "Certificate Generated!",
        description: "Your certificate has been generated and downloaded successfully.",
      });
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast({
        title: "Generation Failed",
        description: "There was an error generating your certificate. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  }, [selectedTemplate, formData, toast]);

  // 表单验证
  const isFormValid = formData.recipientName && formData.date && 
                     formData.signature && formData.details;

  return (
    <div className="w-full max-w-4xl mx-auto">
      <div className="space-y-8">
        {/* 响应式布局：根据模板方向调整比例 */}
        <div className={`grid grid-cols-1 gap-8 ${
          selectedTemplate.orientation === 'landscape' 
            ? 'lg:grid-cols-3' // 横向模板：1:2比例
            : 'lg:grid-cols-2'  // 竖向模板：1:1比例
        }`}>
          
          {/* 左侧：表单区域 */}
          <div className={`space-y-6 ${
            selectedTemplate.orientation === 'landscape' 
              ? 'lg:col-span-1' // 横向模板：占1列
              : ''              // 竖向模板：默认占1列
          }`}>
            <SimpleCertificateForm
              template={selectedTemplate}
              formData={formData}
              onFormDataChange={handleFormDataChange}
            />

            {/* PDF生成按钮 */}
            <Button
              onClick={handleGeneratePDF}
              disabled={!isFormValid || isGenerating}
              className="w-full"
            >
              {isGenerating ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating PDF... {generationProgress}%
                </>
              ) : (
                <>
                  <Download className="mr-2 h-4 w-4" />
                  Download Certificate
                </>
              )}
            </Button>

            {/* 进度条 */}
            {isGenerating && (
              <Progress value={generationProgress} className="w-full" />
            )}
          </div>

          {/* 右侧：预览区域 */}
          <div className={`lg:sticky lg:top-8 ${
            selectedTemplate.orientation === 'landscape' 
              ? 'lg:col-span-2' // 横向模板：占2列，实现1:2比例
              : ''              // 竖向模板：默认占1列
          }`}>
            <CertificatePreview
              template={selectedTemplate}
              formData={formData}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
```

## 📝 表单处理逻辑

### SimpleCertificateForm 组件
```typescript
// src/components/certificate/SimpleCertificateForm.tsx

interface SimpleCertificateFormProps {
  template: CertificateTemplate;
  formData: CertificateData;
  onFormDataChange: (data: CertificateData) => void;
}

export default function SimpleCertificateForm({
  template,
  formData,
  onFormDataChange,
}: SimpleCertificateFormProps) {
  // 输入处理函数
  const handleInputChange = (field: keyof CertificateData, value: string) => {
    onFormDataChange({
      ...formData,
      [field]: value,
    });
  };

  return (
    <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 space-y-6">
      {/* 获奖者姓名字段 */}
      <div>
        <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
          Name
        </label>
        <textarea
          id="name"
          placeholder="Name of the awardee"
          value={formData.recipientName}
          onChange={(e) => handleInputChange('recipientName', e.target.value)}
          maxLength={template.constraints.nameMaxLength} // 动态长度限制
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={2}
        />
      </div>

      {/* 副标题/详细信息字段 */}
      <div>
        <label htmlFor="subtitle" className="block text-sm font-medium text-gray-700 mb-2">
          Subtitle
        </label>
        <textarea
          id="subtitle"
          placeholder="Appears under the name of the awardee"
          value={formData.details}
          onChange={(e) => handleInputChange('details', e.target.value)}
          maxLength={template.constraints.detailsMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={3}
        />
      </div>

      {/* 日期字段 */}
      <div>
        <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
          Date
        </label>
        <textarea
          id="date"
          placeholder=""
          value={formData.date}
          onChange={(e) => handleInputChange('date', e.target.value)}
          maxLength={template.constraints.dateMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={2}
        />
      </div>

      {/* 签名字段 */}
      <div>
        <label htmlFor="signature" className="block text-sm font-medium text-gray-700 mb-2">
          Signature
        </label>
        <textarea
          id="signature"
          placeholder="Name of signer"
          value={formData.signature}
          onChange={(e) => handleInputChange('signature', e.target.value)}
          maxLength={template.constraints.signatureMaxLength}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
          rows={2}
        />
      </div>
    </div>
  );
}
```

### 表单验证逻辑
```typescript
// 表单验证配置 (来自模板配置)
interface TemplateConstraints {
  nameMaxLength: number;    // 姓名最大长度 (通常50)
  nameMinLength: number;    // 姓名最小长度 (通常1)
  dateMaxLength: number;    // 日期最大长度 (通常20)
  dateMinLength: number;    // 日期最小长度 (通常1)
  signatureMaxLength: number; // 签名最大长度 (通常30)
  signatureMinLength: number; // 签名最小长度 (通常1)
  detailsMaxLength: number;   // 详情最大长度 (通常200)
  detailsMinLength: number;   // 详情最小长度 (通常10)
}

interface TemplateValidation {
  namePattern: RegExp;      // 姓名格式验证 /^[a-zA-Z\s\-\.\']+$/
  datePattern: RegExp;      // 日期格式验证 /^[a-zA-Z0-9\s\,\-\.]+$/
  signaturePattern: RegExp; // 签名格式验证 /^[a-zA-Z\s\-\.\']+$/
  detailsPattern: RegExp;   // 详情格式验证
}

// 验证函数
const validateForm = (formData: CertificateData, template: CertificateTemplate): boolean => {
  const { constraints, validation } = template;
  
  // 检查必填字段
  if (!formData.recipientName || !formData.date || !formData.signature || !formData.details) {
    return false;
  }
  
  // 检查长度限制
  if (formData.recipientName.length > constraints.nameMaxLength ||
      formData.date.length > constraints.dateMaxLength ||
      formData.signature.length > constraints.signatureMaxLength ||
      formData.details.length > constraints.detailsMaxLength ||
      formData.details.length < constraints.detailsMinLength) {
    return false;
  }
  
  // 检查格式验证
  if (!validation.namePattern.test(formData.recipientName) ||
      !validation.datePattern.test(formData.date) ||
      !validation.signaturePattern.test(formData.signature)) {
    return false;
  }
  
  return true;
};
```

## 🖼️ 预览组件实现

### CertificatePreview 组件
```typescript
// src/components/certificate/CertificatePreview.tsx

interface CertificatePreviewProps {
  template: CertificateTemplate;
  formData: CertificateData;
}

export default function CertificatePreview({
  template,
  formData,
}: CertificatePreviewProps) {
  // 根据模板方向确定预览容器样式
  const previewContainerClass = template.orientation === 'landscape'
    ? 'aspect-[4/3] w-full max-w-4xl mx-auto' // 横向模板：更大展示空间
    : 'aspect-[3/4] w-full max-w-md mx-auto';  // 竖向模板：标准尺寸

  return (
    <div className="w-full">
      <div className={`${previewContainerClass} relative`}>
        <div
          className="relative h-full w-full rounded-lg shadow-lg overflow-hidden"
          style={{
            backgroundColor: template.style.colors.background,
            fontFamily: template.style.fonts.body.family,
          }}
        >
          {/* 背景图片支持 */}
          {template.backgroundImage && (
            <div className="absolute inset-0">
              <img
                src={template.backgroundImage}
                alt="Certificate background"
                className="w-full h-full object-cover"
              />
            </div>
          )}

          {/* 证书内容容器 */}
          <div className={`relative h-full flex flex-col ${
            template.orientation === 'landscape' ? 'p-4' : 'p-6'
          }`}>

            {/* 证书标题 - 始终显示 */}
            <div className={`text-center ${
              template.orientation === 'landscape' ? 'mb-4' : 'mb-8'
            }`}>
              <h1
                className="font-bold leading-tight"
                style={{
                  color: template.style.colors.primary,
                  fontFamily: template.style.fonts.title.family,
                  fontSize: `${Math.max(template.style.fonts.title.size * (template.orientation === 'landscape' ? 0.3 : 0.4), 14)}px`,
                  fontWeight: template.style.fonts.title.weight,
                }}
              >
                CERTIFICATE OF ACHIEVEMENT
              </h1>
            </div>

            {/* 装饰线 */}
            <div className={`flex justify-center ${
              template.orientation === 'landscape' ? 'mb-3' : 'mb-6'
            }`}>
              <div
                className={`${
                  template.orientation === 'landscape' ? 'w-16 lg:w-20' : 'w-20'
                } h-0.5 opacity-30`}
                style={{ backgroundColor: template.style.colors.secondary }}
              />
            </div>

            {/* 获奖者姓名 - 条件渲染 */}
            {formData.recipientName && (
              <div className={`text-center ${
                template.orientation === 'landscape' ? 'mb-3' : 'mb-6'
              }`}>
                <div className="text-xs text-gray-500 mb-1">This certificate is presented to</div>
                <div
                  className="font-semibold leading-tight"
                  style={{
                    color: template.style.fonts.name.color,
                    fontFamily: template.style.fonts.name.family,
                    fontSize: `${Math.max(template.style.fonts.name.size * (template.orientation === 'landscape' ? 0.4 : 0.5), 12)}px`,
                    fontWeight: template.style.fonts.name.weight,
                  }}
                >
                  {formData.recipientName}
                </div>
              </div>
            )}

            {/* 详细信息 - 条件渲染 */}
            {formData.details && (
              <div className={`text-center flex-1 flex items-center justify-center ${
                template.orientation === 'landscape' ? 'mb-4' : 'mb-8'
              }`}>
                <div
                  className="leading-relaxed px-4"
                  style={{
                    color: template.style.fonts.body.color,
                    fontFamily: template.style.fonts.body.family,
                    fontSize: `${Math.max(template.style.fonts.body.size * (template.orientation === 'landscape' ? 0.5 : 0.6), 9)}px`,
                    fontWeight: template.style.fonts.body.weight,
                  }}
                >
                  {formData.details}
                </div>
              </div>
            )}

            {/* 日期和签名 - 条件渲染 */}
            <div className="flex justify-between items-end">
              {/* 日期 */}
              {formData.date && (
                <div className="text-left">
                  <div className="text-xs text-gray-500 mb-1">Date</div>
                  <div
                    className="pb-1"
                    style={{
                      color: template.style.fonts.body.color,
                      fontFamily: template.style.fonts.body.family,
                      fontSize: `${Math.max(template.style.fonts.body.size * (template.orientation === 'landscape' ? 0.5 : 0.6), 9)}px`,
                    }}
                  >
                    {formData.date}
                  </div>
                </div>
              )}

              {/* 签名 */}
              {formData.signature && (
                <div className="text-right">
                  <div className="text-xs text-gray-500 mb-1">Signature</div>
                  <div
                    className="pb-1 text-right"
                    style={{
                      color: template.style.fonts.signature.color,
                      fontFamily: template.style.fonts.signature.family,
                      fontSize: `${Math.max(template.style.fonts.signature.size * (template.orientation === 'landscape' ? 0.4 : 0.5), 10)}px`,
                      fontWeight: template.style.fonts.signature.weight,
                    }}
                  >
                    {formData.signature}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* 模板特定装饰元素 */}
          {template.id === 'achievement-template-1' && (
            <>
              <div
                className="absolute top-4 left-4 w-8 h-8 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.primary }}
              />
              <div
                className="absolute bottom-4 right-4 w-6 h-6 rounded-full opacity-10"
                style={{ backgroundColor: template.style.colors.secondary }}
              />
            </>
          )}
        </div>
      </div>
    </div>
  );
}
```

## 📄 PDF生成流程

### PDFGenerator 类实现
```typescript
// src/lib/pdf-generator.ts

import { PDFDocument, PDFPage, PDFFont, StandardFonts, rgb, degrees } from 'pdf-lib';
import { CertificateTemplate, CertificateData } from '@/types/certificate';

export class PDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;
  private pdfDoc: PDFDocument | null = null;
  private page: PDFPage | null = null;
  private fonts: { [key: string]: PDFFont } = {};

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
  }

  /**
   * 生成PDF文档
   */
  async generate(): Promise<Uint8Array> {
    const startTime = performance.now();

    try {
      // 1. 创建PDF文档
      this.pdfDoc = await PDFDocument.create();

      // 2. 设置文档元数据
      this.setDocumentMetadata();

      // 3. 根据模板方向添加页面
      const pageSize = this.getPageSize();
      this.page = this.pdfDoc.addPage(pageSize);

      // 4. 加载字体
      await this.loadFonts();

      // 5. 绘制证书内容
      await this.drawCertificate();

      // 6. 返回PDF字节数组
      const pdfBytes = await this.pdfDoc.save();

      const endTime = performance.now();
      console.log(`PDF generation completed in ${endTime - startTime}ms`);

      return pdfBytes;
    } catch (error) {
      console.error('PDF generation error:', error);
      throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * 生成PDF并自动下载
   */
  async generateAndDownload(): Promise<void> {
    const pdfBytes = await this.generate();
    const blob = new Blob([pdfBytes], { type: 'application/pdf' });
    const filename = `certificate-${this.data.recipientName.replace(/[^a-zA-Z0-9]/g, '_')}-${Date.now()}.pdf`;

    // 创建下载链接
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * 设置PDF文档元数据
   */
  private setDocumentMetadata(): void {
    if (!this.pdfDoc) return;

    this.pdfDoc.setTitle(`Certificate - ${this.data.recipientName}`);
    this.pdfDoc.setSubject('Certificate of Achievement');
    this.pdfDoc.setAuthor('Certificate Maker');
    this.pdfDoc.setCreator('Certificate Maker - https://certificate-maker.com');
    this.pdfDoc.setProducer('PDF-lib');
    this.pdfDoc.setCreationDate(new Date());
    this.pdfDoc.setModificationDate(new Date());
  }

  /**
   * 获取页面尺寸
   */
  private getPageSize(): [number, number] {
    // A4 尺寸 (595.28 x 841.89 points)
    if (this.template.orientation === 'landscape') {
      return [841.89, 595.28]; // 横向
    } else {
      return [595.28, 841.89]; // 竖向
    }
  }

  /**
   * 加载字体
   */
  private async loadFonts(): Promise<void> {
    if (!this.pdfDoc) return;

    // 加载标准字体
    this.fonts.helvetica = await this.pdfDoc.embedFont(StandardFonts.Helvetica);
    this.fonts.helveticaBold = await this.pdfDoc.embedFont(StandardFonts.HelveticaBold);
    this.fonts.timesRoman = await this.pdfDoc.embedFont(StandardFonts.TimesRoman);
    this.fonts.timesRomanBold = await this.pdfDoc.embedFont(StandardFonts.TimesRomanBold);
  }

  /**
   * 绘制证书内容
   */
  private async drawCertificate(): Promise<void> {
    if (!this.page || !this.pdfDoc) return;

    const { width, height } = this.page.getSize();

    // 1. 绘制背景
    await this.drawBackground();

    // 2. 绘制边框
    this.drawBorder();

    // 3. 绘制标题
    this.drawTitle();

    // 4. 绘制获奖者姓名
    this.drawRecipientName();

    // 5. 绘制详细信息
    this.drawDetails();

    // 6. 绘制日期和签名
    this.drawDateAndSignature();

    // 7. 绘制装饰元素
    this.drawDecorations();
  }

  /**
   * 绘制背景
   */
  private async drawBackground(): Promise<void> {
    if (!this.page || !this.template.backgroundImage) return;

    try {
      // 如果有背景图片，加载并绘制
      const imageBytes = await fetch(this.template.backgroundImage).then(res => res.arrayBuffer());
      const image = await this.pdfDoc!.embedPng(new Uint8Array(imageBytes));

      const { width, height } = this.page.getSize();
      this.page.drawImage(image, {
        x: 0,
        y: 0,
        width,
        height,
      });
    } catch (error) {
      console.warn('Failed to load background image:', error);
      // 如果背景图片加载失败，使用纯色背景
      this.page.drawRectangle({
        x: 0,
        y: 0,
        width: this.page.getSize().width,
        height: this.page.getSize().height,
        color: rgb(1, 1, 1), // 白色背景
      });
    }
  }

  /**
   * 绘制边框
   */
  private drawBorder(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const margin = 40;
    const borderColor = this.hexToRgb(this.template.style.colors.primary);

    // 外边框
    this.page.drawRectangle({
      x: margin,
      y: margin,
      width: width - 2 * margin,
      height: height - 2 * margin,
      borderColor: borderColor,
      borderWidth: 2,
    });

    // 内边框
    const innerMargin = margin + 20;
    this.page.drawRectangle({
      x: innerMargin,
      y: innerMargin,
      width: width - 2 * innerMargin,
      height: height - 2 * innerMargin,
      borderColor: borderColor,
      borderWidth: 1,
      opacity: 0.5,
    });
  }

  /**
   * 绘制标题
   */
  private drawTitle(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const titleFont = this.fonts.timesRomanBold;
    const titleSize = 36;
    const titleColor = this.hexToRgb(this.template.style.colors.primary);

    const title = 'CERTIFICATE OF ACHIEVEMENT';
    const titleWidth = titleFont.widthOfTextAtSize(title, titleSize);

    this.page.drawText(title, {
      x: (width - titleWidth) / 2,
      y: height - 150,
      size: titleSize,
      font: titleFont,
      color: titleColor,
    });
  }

  /**
   * 绘制获奖者姓名
   */
  private drawRecipientName(): void {
    if (!this.page || !this.data.recipientName) return;

    const { width, height } = this.page.getSize();
    const nameFont = this.fonts.timesRomanBold;
    const nameSize = 28;
    const nameColor = this.hexToRgb(this.template.style.colors.text);

    // "This certificate is presented to" 文本
    const presentedToText = 'This certificate is presented to';
    const presentedToFont = this.fonts.helvetica;
    const presentedToSize = 14;
    const presentedToWidth = presentedToFont.widthOfTextAtSize(presentedToText, presentedToSize);

    this.page.drawText(presentedToText, {
      x: (width - presentedToWidth) / 2,
      y: height - 250,
      size: presentedToSize,
      font: presentedToFont,
      color: rgb(0.4, 0.4, 0.4),
    });

    // 获奖者姓名
    const nameWidth = nameFont.widthOfTextAtSize(this.data.recipientName, nameSize);
    this.page.drawText(this.data.recipientName, {
      x: (width - nameWidth) / 2,
      y: height - 290,
      size: nameSize,
      font: nameFont,
      color: nameColor,
    });
  }

  /**
   * 绘制详细信息
   */
  private drawDetails(): void {
    if (!this.page || !this.data.details) return;

    const { width, height } = this.page.getSize();
    const detailsFont = this.fonts.helvetica;
    const detailsSize = 16;
    const detailsColor = this.hexToRgb(this.template.style.colors.text);

    // 处理多行文本
    const maxWidth = 400;
    const lines = this.wrapText(this.data.details, detailsFont, detailsSize, maxWidth);

    lines.forEach((line, index) => {
      const lineWidth = detailsFont.widthOfTextAtSize(line, detailsSize);
      this.page!.drawText(line, {
        x: (width - lineWidth) / 2,
        y: height - 380 - (index * 25),
        size: detailsSize,
        font: detailsFont,
        color: detailsColor,
      });
    });
  }

  /**
   * 绘制日期和签名
   */
  private drawDateAndSignature(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const font = this.fonts.helvetica;
    const size = 14;
    const color = this.hexToRgb(this.template.style.colors.text);
    const y = 120;

    // 日期
    if (this.data.date) {
      this.page.drawText('Date', {
        x: 100,
        y: y + 20,
        size: 12,
        font: font,
        color: rgb(0.4, 0.4, 0.4),
      });

      this.page.drawText(this.data.date, {
        x: 100,
        y: y,
        size: size,
        font: font,
        color: color,
      });
    }

    // 签名
    if (this.data.signature) {
      const signatureFont = this.fonts.timesRomanBold;
      const signatureSize = 18;

      this.page.drawText('Signature', {
        x: width - 200,
        y: y + 20,
        size: 12,
        font: font,
        color: rgb(0.4, 0.4, 0.4),
      });

      this.page.drawText(this.data.signature, {
        x: width - 200,
        y: y,
        size: signatureSize,
        font: signatureFont,
        color: color,
      });
    }
  }

  /**
   * 绘制装饰元素
   */
  private drawDecorations(): void {
    if (!this.page) return;

    const { width, height } = this.page.getSize();
    const primaryColor = this.hexToRgb(this.template.style.colors.primary);
    const secondaryColor = this.hexToRgb(this.template.style.colors.secondary);

    // 装饰线
    this.page.drawLine({
      start: { x: width / 2 - 50, y: height - 200 },
      end: { x: width / 2 + 50, y: height - 200 },
      thickness: 2,
      color: secondaryColor,
      opacity: 0.6,
    });

    // 装饰圆圈
    this.page.drawCircle({
      x: 80,
      y: height - 80,
      size: 15,
      color: primaryColor,
      opacity: 0.1,
    });

    this.page.drawCircle({
      x: width - 80,
      y: 80,
      size: 12,
      color: secondaryColor,
      opacity: 0.1,
    });
  }

  /**
   * 工具函数：十六进制颜色转RGB
   */
  private hexToRgb(hex: string): { r: number; g: number; b: number } {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
    if (!result) return { r: 0, g: 0, b: 0 };

    return {
      r: parseInt(result[1], 16) / 255,
      g: parseInt(result[2], 16) / 255,
      b: parseInt(result[3], 16) / 255,
    };
  }

  /**
   * 工具函数：文本换行
   */
  private wrapText(text: string, font: PDFFont, size: number, maxWidth: number): string[] {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';

    for (const word of words) {
      const testLine = currentLine ? `${currentLine} ${word}` : word;
      const testWidth = font.widthOfTextAtSize(testLine, size);

      if (testWidth <= maxWidth) {
        currentLine = testLine;
      } else {
        if (currentLine) {
          lines.push(currentLine);
        }
        currentLine = word;
      }
    }

    if (currentLine) {
      lines.push(currentLine);
    }

    return lines;
  }
}

/**
 * 便捷函数：生成PDF并下载
 */
export async function generateCertificatePDF(
  template: CertificateTemplate,
  data: CertificateData
): Promise<void> {
  const generator = new PDFGenerator(template, data);
  await generator.generateAndDownload();
}
```

## 📊 数据结构定义

### 核心类型接口
```typescript
// src/types/certificate.ts

// 证书分类枚举
export enum CertificateCategory {
  ACHIEVEMENT = 'achievement',
  COMPLETION = 'completion',
  PARTICIPATION = 'participation',
  EXCELLENCE = 'excellence',
}

// 证书数据接口
export interface CertificateData {
  templateId: string;      // 模板ID
  recipientName: string;   // 获奖者姓名
  date: string;           // 日期
  signature: string;      // 签名
  details: string;        // 详细信息/副标题
}

// 字体配置接口
export interface FontConfig {
  family: string;         // 字体族 'Playfair Display', 'Inter', 'Dancing Script'
  size: number;          // 字体大小
  weight: number;        // 字体粗细 400, 600, 700
  color: string;         // 字体颜色 '#1F2937'
}

// 颜色方案接口
export interface ColorScheme {
  primary: string;       // 主色调 '#1E40AF'
  secondary: string;     // 次色调 '#3B82F6'
  background: string;    // 背景色 '#FFFFFF'
  text: string;         // 文本色 '#1F2937'
  border: string;       // 边框色 '#1E40AF'
}

// 布局位置接口
export interface FixedPosition {
  x: number;            // X坐标
  y: number;            // Y坐标
  width: number;        // 宽度
  height: number;       // 高度
  align: 'left' | 'center' | 'right'; // 对齐方式
  fontSize: number;     // 字体大小
  fontFamily: string;   // 字体族
  color: string;        // 颜色
  fontWeight?: number;  // 字体粗细
}

// 模板约束接口
export interface TemplateConstraints {
  nameMaxLength: number;    // 姓名最大长度
  nameMinLength: number;    // 姓名最小长度
  dateMaxLength: number;    // 日期最大长度
  dateMinLength: number;    // 日期最小长度
  signatureMaxLength: number; // 签名最大长度
  signatureMinLength: number; // 签名最小长度
  detailsMaxLength: number;   // 详情最大长度
  detailsMinLength: number;   // 详情最小长度
}

// 模板验证接口
export interface TemplateValidation {
  namePattern: RegExp;      // 姓名格式验证
  datePattern: RegExp;      // 日期格式验证
  signaturePattern: RegExp; // 签名格式验证
  detailsPattern: RegExp;   // 详情格式验证
}

// 证书模板接口
export interface CertificateTemplate {
  // 基础信息
  id: string;                    // 唯一标识符
  name: string;                  // 模板名称
  displayName: string;           // 显示名称
  description: string;           // 描述

  // 分类信息
  category: CertificateCategory; // 分类
  tags: string[];               // 标签

  // 视觉属性
  preview: string;              // 预览图路径
  backgroundImage?: string;     // 背景图路径
  orientation: 'portrait' | 'landscape'; // 方向
  aspectRatio: number;          // 宽高比

  // SEO属性
  seoTitle: string;             // SEO标题
  seoDescription: string;       // SEO描述
  seoKeywords: string[];        // SEO关键词

  // 样式配置
  style: {
    background: string;         // 背景色
    border: string;            // 边框样式
    colors: ColorScheme;       // 颜色方案
    fonts: {
      title: FontConfig;       // 标题字体
      name: FontConfig;        // 姓名字体
      body: FontConfig;        // 正文字体
      signature: FontConfig;   // 签名字体
    };
  };

  // 布局配置
  layout: {
    title: FixedPosition;      // 标题位置
    name: FixedPosition;       // 姓名位置
    date: FixedPosition;       // 日期位置
    signature: FixedPosition;  // 签名位置
    details: FixedPosition;    // 详情位置
  };

  // 约束和验证
  constraints: TemplateConstraints;
  validation: TemplateValidation;
}

// 分类配置接口
export interface TemplateCategory {
  id: CertificateCategory;     // 分类ID
  displayName: string;         // 显示名称
  urlSlug: string;            // URL slug
  description: string;         // 描述
  metaTitle: string;          // Meta标题
  metaDescription: string;    // Meta描述
  keywords: string[];         // 关键词
  templateCount: number;      // 模板数量
  popularity: string;         // 受欢迎程度
  icon: string;              // 图标
}
```

## 🗄️ 模板管理系统

### TemplateManager 类
```typescript
// src/lib/template-manager.ts

import { CertificateCategory, CertificateTemplate, TemplateCategory } from '@/types/certificate';
import { CERTIFICATE_TEMPLATES, TEMPLATE_CATEGORIES } from './certificate-templates';

export class TemplateManager {
  /**
   * 根据分类获取模板列表
   */
  static getTemplatesByCategory(category: CertificateCategory): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES.filter(template => template.category === category);
  }

  /**
   * 根据模板ID获取模板
   */
  static getTemplateById(id: string): CertificateTemplate | undefined {
    return CERTIFICATE_TEMPLATES.find(template => template.id === id);
  }

  /**
   * 根据URL slug获取分类枚举
   */
  static getCategoryBySlug(slug: string): CertificateCategory | null {
    const categoryMap: Record<string, CertificateCategory> = {
      'achievement-certificates': CertificateCategory.ACHIEVEMENT,
      'completion-certificates': CertificateCategory.COMPLETION,
      'participation-certificates': CertificateCategory.PARTICIPATION,
      'excellence-certificates': CertificateCategory.EXCELLENCE,
    };
    return categoryMap[slug] || null;
  }

  /**
   * 根据URL slug获取分类配置
   */
  static getCategoryConfigBySlug(slug: string): TemplateCategory | null {
    return TEMPLATE_CATEGORIES.find(category => category.urlSlug === slug) || null;
  }

  /**
   * 获取所有分类
   */
  static getAllCategories(): TemplateCategory[] {
    return TEMPLATE_CATEGORIES;
  }

  /**
   * 获取所有模板
   */
  static getAllTemplates(): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES;
  }

  /**
   * 根据方向获取模板
   */
  static getTemplatesByOrientation(orientation: 'portrait' | 'landscape'): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES.filter(template => template.orientation === orientation);
  }

  /**
   * 根据标签搜索模板
   */
  static searchTemplatesByTags(tags: string[]): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES.filter(template =>
      tags.some(tag => template.tags.includes(tag))
    );
  }

  /**
   * 验证模板ID
   */
  static isValidTemplateId(id: string): boolean {
    return CERTIFICATE_TEMPLATES.some(template => template.id === id);
  }

  /**
   * 获取分类统计信息
   */
  static getCategoryStats(category: CertificateCategory): {
    totalTemplates: number;
    landscapeCount: number;
    portraitCount: number;
  } {
    const templates = this.getTemplatesByCategory(category);
    return {
      totalTemplates: templates.length,
      landscapeCount: templates.filter(t => t.orientation === 'landscape').length,
      portraitCount: templates.filter(t => t.orientation === 'portrait').length,
    };
  }
}
```

### 模板配置示例
```typescript
// src/lib/certificate-templates.ts

// Achievement 分类模板示例
export const ACHIEVEMENT_TEMPLATE_1: CertificateTemplate = {
  // 基础信息
  id: 'achievement-template-1',
  name: 'achievement-template-1',
  displayName: 'Classic Achievement Certificate',
  description: 'Professional certificate for recognizing outstanding achievements and accomplishments',

  // 分类信息
  category: CertificateCategory.ACHIEVEMENT,
  tags: ['achievement', 'professional', 'classic', 'business', 'award'],

  // 视觉属性
  preview: '/templates/achievement-1.png',
  backgroundImage: '/templates/achievement-1.png',
  orientation: 'landscape' as const,
  aspectRatio: 4/3,

  // SEO属性
  seoTitle: 'Classic Achievement Certificate Template | Professional Award Certificates',
  seoDescription: 'Create professional achievement certificates with our classic template. Perfect for recognizing outstanding accomplishments and achievements.',
  seoKeywords: ['achievement certificate', 'award template', 'professional certificate', 'accomplishment recognition'],

  // 样式配置
  style: {
    background: '#FFFFFF',
    border: '3pt solid #1E40AF',
    colors: {
      primary: '#1E40AF',
      secondary: '#3B82F6',
      background: '#FFFFFF',
      text: '#1F2937',
      border: '#1E40AF',
    },
    fonts: {
      title: {
        family: 'Playfair Display',
        size: 36,
        weight: 700,
        color: '#1E40AF',
      },
      name: {
        family: 'Playfair Display',
        size: 28,
        weight: 600,
        color: '#1F2937',
      },
      body: {
        family: 'Inter',
        size: 14,
        weight: 400,
        color: '#374151',
      },
      signature: {
        family: 'Dancing Script',
        size: 24,
        weight: 400,
        color: '#1F2937',
      },
    },
  },

  // 布局配置 (基于A4尺寸 842x595 points for landscape)
  layout: {
    title: {
      x: 421,     // 居中 (842/2)
      y: 450,     // 距离底部145px
      width: 400,
      height: 50,
      align: 'center' as const,
      fontSize: 36,
      fontFamily: 'Playfair Display',
      color: '#1E40AF',
      fontWeight: 700,
    },
    name: {
      x: 421,     // 居中
      y: 350,     // 标题下方100px
      width: 400,
      height: 40,
      align: 'center' as const,
      fontSize: 28,
      fontFamily: 'Playfair Display',
      color: '#1F2937',
      fontWeight: 600,
    },
    details: {
      x: 421,     // 居中
      y: 250,     // 姓名下方100px
      width: 500,
      height: 80,
      align: 'center' as const,
      fontSize: 14,
      fontFamily: 'Inter',
      color: '#374151',
      fontWeight: 400,
    },
    date: {
      x: 100,     // 左侧
      y: 100,     // 距离底部100px
      width: 150,
      height: 30,
      align: 'left' as const,
      fontSize: 14,
      fontFamily: 'Inter',
      color: '#374151',
      fontWeight: 400,
    },
    signature: {
      x: 592,     // 右侧 (842-250)
      y: 100,     // 距离底部100px
      width: 150,
      height: 30,
      align: 'right' as const,
      fontSize: 24,
      fontFamily: 'Dancing Script',
      color: '#1F2937',
      fontWeight: 400,
    },
  },

  // 约束配置
  constraints: {
    nameMaxLength: 50,
    nameMinLength: 1,
    dateMaxLength: 20,
    dateMinLength: 1,
    signatureMaxLength: 30,
    signatureMinLength: 1,
    detailsMaxLength: 200,
    detailsMinLength: 10,
  },

  // 验证配置
  validation: {
    namePattern: /^[a-zA-Z\s\-\.\']+$/,
    datePattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
    signaturePattern: /^[a-zA-Z\s\-\.\']+$/,
    detailsPattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
  },
};

// 分类配置
export const TEMPLATE_CATEGORIES: TemplateCategory[] = [
  {
    id: CertificateCategory.ACHIEVEMENT,
    displayName: 'Achievement Certificates',
    urlSlug: 'achievement-certificates',
    description: 'Professional certificates for recognizing outstanding achievements and accomplishments',
    metaTitle: 'Achievement Certificate Templates | Professional Award Certificates',
    metaDescription: 'Create professional achievement certificates online. Perfect for recognizing outstanding accomplishments, awards, and achievements with customizable templates.',
    keywords: ['achievement certificate', 'award certificate', 'accomplishment recognition', 'professional certificate'],
    templateCount: 3,
    popularity: 'Most Popular',
    icon: 'trophy',
  },
  // ... 其他分类配置
];

// 导出所有模板
export const CERTIFICATE_TEMPLATES: CertificateTemplate[] = [
  ACHIEVEMENT_TEMPLATE_1,
  // ... 其他模板
];
```

## 🚀 部署和迁移步骤

### 1. 依赖安装
```json
// package.json
{
  "dependencies": {
    "next": "^14.0.0",
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "pdf-lib": "^1.17.1",
    "lucide-react": "^0.263.1",
    "@radix-ui/react-toast": "^1.1.4",
    "@radix-ui/react-progress": "^1.0.3",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^1.14.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.0.0",
    "@types/react-dom": "^18.0.0",
    "autoprefixer": "^10.0.0",
    "postcss": "^8.0.0",
    "tailwindcss": "^3.3.0",
    "typescript": "^5.0.0"
  }
}
```

### 2. 目录结构创建
```bash
mkdir -p src/{app,components,lib,types,hooks}
mkdir -p src/app/{certificate-templates,api}
mkdir -p src/components/{certificate,ui,layout,common}
mkdir -p public/templates
```

### 3. 核心文件迁移清单
```
必需文件:
├── src/types/certificate.ts                    # 类型定义
├── src/lib/certificate-templates.ts            # 模板配置
├── src/lib/template-manager.ts                 # 模板管理器
├── src/lib/pdf-generator.ts                    # PDF生成器
├── src/lib/utils.ts                           # 工具函数
├── src/components/certificate/
│   ├── CategoryDetailPage.tsx                  # 分类详情页
│   ├── CertificateMaker.tsx                   # 证书生成器
│   ├── SimpleCertificateForm.tsx              # 表单组件
│   ├── CertificatePreview.tsx                 # 预览组件
│   ├── VisualTemplateCard.tsx                 # 模板卡片
│   └── BreadcrumbNavigation.tsx               # 面包屑导航
├── src/app/certificate-templates/
│   └── [category]/page.tsx                    # 动态路由页面
└── public/templates/                          # 模板图片资源
    ├── achievement-1.png
    ├── achievement-2.png
    └── ...
```

### 4. 环境配置
```env
# .env.local
NEXT_PUBLIC_BASE_URL=https://your-domain.com
NEXT_PUBLIC_SITE_NAME=Certificate Maker
```

### 5. Tailwind CSS 配置
```javascript
// tailwind.config.js
module.exports = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      fontFamily: {
        'playfair': ['Playfair Display', 'serif'],
        'inter': ['Inter', 'sans-serif'],
        'dancing': ['Dancing Script', 'cursive'],
      },
      aspectRatio: {
        '3/4': '3 / 4',
        '4/3': '4 / 3',
      },
    },
  },
  plugins: [],
}
```

### 6. 验证步骤
1. **路由测试**: 访问 `/certificate-templates/achievement-certificates/`
2. **模板加载**: 确认模板列表正确显示
3. **表单功能**: 测试所有表单字段输入
4. **预览功能**: 确认实时预览正常工作
5. **PDF生成**: 测试PDF下载功能
6. **响应式**: 测试不同屏幕尺寸的布局

## 📝 注意事项

### 性能优化
- 使用 `React.memo` 优化组件重渲染
- 实现图片懒加载
- PDF生成使用 Web Workers (可选)
- 模板数据使用静态生成

### 安全考虑
- 输入验证和清理
- XSS 防护
- 文件上传限制
- API 速率限制

### SEO优化
- 动态元数据生成
- 结构化数据标记
- 站点地图自动生成
- 面包屑导航

### 可访问性
- 键盘导航支持
- 屏幕阅读器兼容
- 颜色对比度优化
- ARIA 标签完整

这个迁移指南提供了完整的实现细节，可以帮助您将 achievement-certificates 功能成功迁移到其他项目中。
```
```
