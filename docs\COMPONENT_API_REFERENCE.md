# 组件API参考文档

## 📋 概述

本文档详细描述了证书生成器系统中所有组件的API接口、属性、方法和使用示例。

## 🏗️ 组件架构图

```
CategoryDetailPage (分类详情页)
├── TemplateSelector (模板选择器)
│   ├── GridDisplay (网格展示)
│   └── CarouselDisplay (轮播展示)
└── CertificateMaker (证书生成器)
    ├── SimpleCertificateForm (表单组件)
    └── CertificatePreview (预览组件)
```

## 📦 核心组件

### CategoryDetailPage

分类详情页面的主组件，负责模板展示和证书生成的整体布局。

#### Props
```typescript
interface CategoryDetailPageProps {
  category: TemplateCategory;           // 分类配置信息
  templates: CertificateTemplate[];     // 该分类下的模板列表
  defaultTemplate: CertificateTemplate | null; // 默认选中的模板
}
```

#### State
```typescript
interface CategoryDetailPageState {
  selectedTemplate: CertificateTemplate | null; // 当前选中的模板
  currentIndex: number;                          // 轮播当前索引
  touchStart: number | null;                     // 触摸开始位置
  touchEnd: number | null;                       // 触摸结束位置
}
```

#### 主要方法
```typescript
// 模板选择处理
const handleTemplateSelect = (template: CertificateTemplate) => void;

// 轮播导航
const goToNext = () => void;
const goToPrevious = () => void;
const goToSlide = (index: number) => void;

// 触摸事件处理
const onTouchStart = (e: TouchEvent) => void;
const onTouchMove = (e: TouchEvent) => void;
const onTouchEnd = () => void;
```

#### 使用示例
```typescript
import { CategoryDetailPage } from '@/components/certificate/CategoryDetailPage';

<CategoryDetailPage
  category={categoryConfig}
  templates={templates}
  defaultTemplate={templates[0]}
/>
```

---

### CertificateMaker

证书生成器主组件，包含表单和预览功能。

#### Props
```typescript
interface CertificateMakerProps {
  selectedTemplate: CertificateTemplate; // 选中的模板
}
```

#### State
```typescript
interface CertificateMakerState {
  formData: CertificateData;      // 表单数据
  isGenerating: boolean;          // 是否正在生成PDF
  generationProgress: number;     // 生成进度 (0-100)
  errors: Record<string, string>; // 表单验证错误
}
```

#### 数据类型
```typescript
interface CertificateData {
  name: string;      // 获得者姓名
  subtitle: string;  // 副标题/详情
  date: string;      // 日期
  signature: string; // 签名
}
```

#### 主要方法
```typescript
// 表单数据更新
const handleFormDataChange = (data: CertificateData) => void;

// PDF生成
const handleGeneratePDF = async () => Promise<void>;

// 表单验证
const validateForm = () => boolean;
const validateField = (field: string, value: string) => boolean;
```

#### 使用示例
```typescript
import { CertificateMaker } from '@/components/certificate/CertificateMaker';

<CertificateMaker selectedTemplate={selectedTemplate} />
```

---

### SimpleCertificateForm

证书表单组件，处理用户输入和验证。

#### Props
```typescript
interface SimpleCertificateFormProps {
  template: CertificateTemplate;                    // 模板配置
  formData: CertificateData;                       // 表单数据
  onFormDataChange: (data: CertificateData) => void; // 数据变更回调
  errors?: Record<string, string>;                  // 验证错误
}
```

#### 表单字段配置
```typescript
interface FormField {
  id: keyof CertificateData;  // 字段ID
  label: string;              // 字段标签
  type: 'text' | 'textarea' | 'date'; // 字段类型
  required: boolean;          // 是否必填
  placeholder: string;        // 占位符
  maxLength: number;          // 最大长度
  validation: RegExp;         // 验证正则
}
```

#### 验证规则
```typescript
const validationRules = {
  name: {
    pattern: /^[a-zA-Z\s\-\.\']+$/,
    minLength: 1,
    maxLength: 50,
    required: true
  },
  subtitle: {
    pattern: /^[a-zA-Z0-9\s\,\.\!\?\-\'\"\(\)]+$/,
    minLength: 10,
    maxLength: 200,
    required: true
  },
  date: {
    pattern: /^[a-zA-Z0-9\s\,\-\.]+$/,
    minLength: 1,
    maxLength: 20,
    required: true
  },
  signature: {
    pattern: /^[a-zA-Z\s\-\.\']+$/,
    minLength: 1,
    maxLength: 30,
    required: true
  }
};
```

#### 使用示例
```typescript
import { SimpleCertificateForm } from '@/components/certificate/SimpleCertificateForm';

<SimpleCertificateForm
  template={template}
  formData={formData}
  onFormDataChange={setFormData}
  errors={errors}
/>
```

---

### CertificatePreview

证书预览组件，实时显示证书效果。

#### Props
```typescript
interface CertificatePreviewProps {
  template: CertificateTemplate; // 模板配置
  formData: CertificateData;     // 表单数据
  className?: string;            // 自定义样式类
}
```

#### 布局计算
```typescript
interface LayoutPosition {
  x: number;        // X坐标
  y: number;        // Y坐标
  width: number;    // 宽度
  height: number;   // 高度
  align: 'left' | 'center' | 'right'; // 对齐方式
  fontSize: number; // 字体大小
  fontFamily: string; // 字体族
  color: string;    // 字体颜色
  fontWeight: number; // 字体粗细
}
```

#### 样式应用
```typescript
const getFieldStyle = (field: string): React.CSSProperties => {
  const layout = template.layout[field];
  const style = template.style.fonts[field];
  
  return {
    position: 'absolute',
    left: `${layout.x}px`,
    top: `${layout.y}px`,
    width: `${layout.width}px`,
    height: `${layout.height}px`,
    fontSize: `${layout.fontSize}px`,
    fontFamily: layout.fontFamily,
    color: layout.color,
    fontWeight: layout.fontWeight,
    textAlign: layout.align,
    // ...其他样式
  };
};
```

#### 使用示例
```typescript
import { CertificatePreview } from '@/components/certificate/CertificatePreview';

<CertificatePreview
  template={template}
  formData={formData}
  className="border rounded-lg"
/>
```

---

### CategoryNavigation

分类导航组件，用于分类间的切换。

#### Props
```typescript
interface CategoryNavigationProps {
  currentCategory?: string;  // 当前分类
  showTitle?: boolean;       // 是否显示标题
  maxCategories?: number;    // 最大显示分类数
}
```

#### 分类配置
```typescript
interface CategoryConfig {
  id: CertificateCategory;
  name: string;
  displayName: string;
  description: string;
  urlSlug: string;
  templateCount: number;
  color: string;
}
```

#### 使用示例
```typescript
import { CategoryNavigation } from '@/components/certificate/CategoryNavigation';

<CategoryNavigation
  currentCategory="achievement"
  showTitle={true}
  maxCategories={4}
/>
```

## 🛠️ 工具类API

### TemplateManager

模板管理工具类，提供模板相关的操作方法。

#### 静态方法
```typescript
class TemplateManager {
  // 根据分类获取模板
  static getTemplatesByCategory(category: CertificateCategory): CertificateTemplate[];
  
  // 根据ID获取模板
  static getTemplateById(id: string): CertificateTemplate | undefined;
  
  // 根据名称获取模板
  static getTemplateByName(name: string): CertificateTemplate | undefined;
  
  // 获取所有模板
  static getAllTemplates(): CertificateTemplate[];
  
  // 根据方向获取模板
  static getTemplatesByOrientation(orientation: 'portrait' | 'landscape'): CertificateTemplate[];
  
  // 根据标签搜索模板
  static searchTemplatesByTags(tags: string[]): CertificateTemplate[];
  
  // 验证模板ID
  static isValidTemplateId(id: string): boolean;
  
  // 根据URL slug获取分类
  static getCategoryBySlug(slug: string): CertificateCategory | null;
  
  // 获取分类配置
  static getCategoryConfig(category: CertificateCategory): TemplateCategory;
  
  // 获取所有分类
  static getAllCategories(): TemplateCategory[];
}
```

### PDFGenerator

PDF生成工具类。

#### 静态方法
```typescript
class PDFGenerator {
  // 生成证书PDF
  static async generateCertificatePDF(
    element: HTMLElement,
    filename: string,
    onProgress?: (progress: number) => void
  ): Promise<void>;
  
  // 配置PDF选项
  static getPDFOptions(template: CertificateTemplate): jsPDF.Options;
  
  // 配置Canvas选项
  static getCanvasOptions(): html2canvas.Options;
}
```

## 🎨 样式系统

### 主题配置
```typescript
interface ThemeConfig {
  colors: {
    primary: string;
    secondary: string;
    background: string;
    text: string;
    border: string;
  };
  fonts: {
    title: FontConfig;
    name: FontConfig;
    body: FontConfig;
    signature: FontConfig;
  };
}

interface FontConfig {
  family: string;
  size: number;
  weight: number;
  color: string;
}
```

### 响应式工具
```typescript
// 断点检测Hook
const useBreakpoint = () => {
  const [breakpoint, setBreakpoint] = useState<'sm' | 'md' | 'lg' | 'xl'>('sm');
  
  useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth;
      if (width >= 1280) setBreakpoint('xl');
      else if (width >= 1024) setBreakpoint('lg');
      else if (width >= 768) setBreakpoint('md');
      else setBreakpoint('sm');
    };
    
    updateBreakpoint();
    window.addEventListener('resize', updateBreakpoint);
    return () => window.removeEventListener('resize', updateBreakpoint);
  }, []);
  
  return breakpoint;
};
```

## 🔧 自定义Hooks

### useTemplateSelection
```typescript
const useTemplateSelection = (templates: CertificateTemplate[], defaultTemplate?: CertificateTemplate) => {
  const [selectedTemplate, setSelectedTemplate] = useState<CertificateTemplate | null>(defaultTemplate || null);
  const [currentIndex, setCurrentIndex] = useState(0);
  
  const selectTemplate = (template: CertificateTemplate) => {
    setSelectedTemplate(template);
    const index = templates.findIndex(t => t.id === template.id);
    setCurrentIndex(index);
  };
  
  const goToNext = () => {
    const nextIndex = (currentIndex + 1) % templates.length;
    setCurrentIndex(nextIndex);
    setSelectedTemplate(templates[nextIndex]);
  };
  
  const goToPrevious = () => {
    const prevIndex = currentIndex === 0 ? templates.length - 1 : currentIndex - 1;
    setCurrentIndex(prevIndex);
    setSelectedTemplate(templates[prevIndex]);
  };
  
  return {
    selectedTemplate,
    currentIndex,
    selectTemplate,
    goToNext,
    goToPrevious
  };
};
```

### useFormValidation
```typescript
const useFormValidation = (template: CertificateTemplate) => {
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const validateField = (field: string, value: string): boolean => {
    const validation = template.validation;
    const constraints = template.constraints;
    
    // 检查必填
    if (constraints[`${field}MinLength`] > 0 && !value.trim()) {
      setErrors(prev => ({ ...prev, [field]: 'This field is required' }));
      return false;
    }
    
    // 检查长度
    if (value.length > constraints[`${field}MaxLength`]) {
      setErrors(prev => ({ ...prev, [field]: 'Value is too long' }));
      return false;
    }
    
    // 检查格式
    const pattern = validation[`${field}Pattern`];
    if (pattern && !pattern.test(value)) {
      setErrors(prev => ({ ...prev, [field]: 'Invalid format' }));
      return false;
    }
    
    // 清除错误
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
    
    return true;
  };
  
  const validateForm = (formData: CertificateData): boolean => {
    const fields = Object.keys(formData) as (keyof CertificateData)[];
    return fields.every(field => validateField(field, formData[field]));
  };
  
  return {
    errors,
    validateField,
    validateForm,
    clearErrors: () => setErrors({})
  };
};
```

---

## 📚 使用示例

### 完整的页面实现
```typescript
import React from 'react';
import { CategoryDetailPage } from '@/components/certificate/CategoryDetailPage';
import { TemplateManager } from '@/lib/template-manager';
import { CertificateCategory } from '@/types/certificate';

interface PageProps {
  params: { category: string };
}

export default function CertificatePage({ params }: PageProps) {
  // 获取分类
  const categoryEnum = TemplateManager.getCategoryBySlug(params.category);
  if (!categoryEnum) {
    return <div>Category not found</div>;
  }
  
  // 获取模板和配置
  const templates = TemplateManager.getTemplatesByCategory(categoryEnum);
  const categoryConfig = TemplateManager.getCategoryConfig(categoryEnum);
  const defaultTemplate = templates[0];
  
  return (
    <div className="min-h-screen bg-gray-50">
      <CategoryDetailPage
        category={categoryConfig}
        templates={templates}
        defaultTemplate={defaultTemplate}
      />
    </div>
  );
}
```

这个API参考文档提供了所有组件的详细接口说明，便于开发者理解和使用证书生成器系统。
