# Achievement Certificates 快速迁移参考

## 🎯 核心流程概览

```
用户访问 /achievement-certificates
    ↓
动态路由解析 [category] = "achievement-certificates"
    ↓
TemplateManager.getCategoryBySlug() 获取分类
    ↓
TemplateManager.getTemplatesByCategory() 获取模板列表
    ↓
CategoryDetailPage 渲染页面
    ↓
用户选择模板 → CertificateMaker 组件
    ↓
用户填写表单 → SimpleCertificateForm
    ↓
实时预览更新 → CertificatePreview
    ↓
用户点击下载 → PDFGenerator.generateAndDownload()
    ↓
PDF文件自动下载
```

## 📁 关键文件清单

### 必需的核心文件 (按优先级)
```
1. src/types/certificate.ts              # 类型定义 (最高优先级)
2. src/lib/certificate-templates.ts      # 模板数据
3. src/lib/template-manager.ts           # 模板管理
4. src/lib/pdf-generator.ts              # PDF生成
5. src/components/certificate/CertificateMaker.tsx    # 主组件
6. src/components/certificate/SimpleCertificateForm.tsx # 表单
7. src/components/certificate/CertificatePreview.tsx   # 预览
8. src/app/certificate-templates/[category]/page.tsx  # 路由
```

### 支持文件
```
src/components/certificate/CategoryDetailPage.tsx     # 页面容器
src/components/certificate/VisualTemplateCard.tsx     # 模板卡片
src/lib/utils.ts                                      # 工具函数
src/hooks/use-toast.tsx                              # Toast通知
```

## 🔧 核心代码片段

### 1. 路由处理 (5分钟)
```typescript
// src/app/certificate-templates/[category]/page.tsx
export default function CategoryPage({ params }: { params: { category: string } }) {
  const categoryConfig = TemplateManager.getCategoryConfigBySlug(params.category);
  const categoryEnum = TemplateManager.getCategoryBySlug(params.category);
  const templates = TemplateManager.getTemplatesByCategory(categoryEnum);
  
  return (
    <CategoryDetailPage
      category={categoryConfig}
      templates={templates}
      defaultTemplate={templates[0]}
    />
  );
}
```

### 2. 主生成器组件 (15分钟)
```typescript
// src/components/certificate/CertificateMaker.tsx
export default function CertificateMaker({ selectedTemplate }) {
  const [formData, setFormData] = useState({
    recipientName: '', date: '', signature: '', details: ''
  });
  
  const handleGeneratePDF = async () => {
    await generateCertificatePDF(selectedTemplate, formData);
  };
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <SimpleCertificateForm 
        formData={formData} 
        onFormDataChange={setFormData} 
      />
      <CertificatePreview 
        template={selectedTemplate} 
        formData={formData} 
      />
    </div>
  );
}
```

### 3. 表单组件 (10分钟)
```typescript
// src/components/certificate/SimpleCertificateForm.tsx
export default function SimpleCertificateForm({ formData, onFormDataChange }) {
  const handleChange = (field, value) => {
    onFormDataChange({ ...formData, [field]: value });
  };
  
  return (
    <div className="space-y-6">
      <textarea 
        value={formData.recipientName}
        onChange={(e) => handleChange('recipientName', e.target.value)}
        placeholder="Name of the awardee"
      />
      <textarea 
        value={formData.details}
        onChange={(e) => handleChange('details', e.target.value)}
        placeholder="Certificate details"
      />
      <textarea 
        value={formData.date}
        onChange={(e) => handleChange('date', e.target.value)}
        placeholder="Date"
      />
      <textarea 
        value={formData.signature}
        onChange={(e) => handleChange('signature', e.target.value)}
        placeholder="Signature"
      />
    </div>
  );
}
```

### 4. 预览组件 (10分钟)
```typescript
// src/components/certificate/CertificatePreview.tsx
export default function CertificatePreview({ template, formData }) {
  return (
    <div className="aspect-[4/3] w-full max-w-2xl mx-auto relative">
      <div className="h-full w-full rounded-lg shadow-lg overflow-hidden bg-white">
        {template.backgroundImage && (
          <img src={template.backgroundImage} className="w-full h-full object-cover" />
        )}
        
        <div className="absolute inset-0 p-6 flex flex-col">
          <h1 className="text-center text-2xl font-bold text-blue-600">
            CERTIFICATE OF ACHIEVEMENT
          </h1>
          
          {formData.recipientName && (
            <div className="text-center mt-8">
              <p className="text-sm text-gray-500">This certificate is presented to</p>
              <h2 className="text-xl font-semibold">{formData.recipientName}</h2>
            </div>
          )}
          
          {formData.details && (
            <div className="text-center mt-6 flex-1 flex items-center">
              <p className="text-sm">{formData.details}</p>
            </div>
          )}
          
          <div className="flex justify-between mt-8">
            {formData.date && <div><p className="text-xs">Date</p><p>{formData.date}</p></div>}
            {formData.signature && <div><p className="text-xs">Signature</p><p>{formData.signature}</p></div>}
          </div>
        </div>
      </div>
    </div>
  );
}
```

### 5. PDF生成器 (20分钟)
```typescript
// src/lib/pdf-generator.ts
import { PDFDocument, StandardFonts, rgb } from 'pdf-lib';

export async function generateCertificatePDF(template, data) {
  const pdfDoc = await PDFDocument.create();
  const page = pdfDoc.addPage([842, 595]); // A4 横向
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  
  // 绘制标题
  page.drawText('CERTIFICATE OF ACHIEVEMENT', {
    x: 421 - (font.widthOfTextAtSize('CERTIFICATE OF ACHIEVEMENT', 36) / 2),
    y: 450,
    size: 36,
    font: font,
    color: rgb(0.12, 0.25, 0.69), // 蓝色
  });
  
  // 绘制姓名
  if (data.recipientName) {
    page.drawText(data.recipientName, {
      x: 421 - (font.widthOfTextAtSize(data.recipientName, 28) / 2),
      y: 350,
      size: 28,
      font: font,
    });
  }
  
  // 绘制详情
  if (data.details) {
    page.drawText(data.details, {
      x: 421 - (font.widthOfTextAtSize(data.details, 16) / 2),
      y: 250,
      size: 16,
      font: font,
    });
  }
  
  // 绘制日期和签名
  if (data.date) {
    page.drawText(data.date, { x: 100, y: 100, size: 14, font: font });
  }
  if (data.signature) {
    page.drawText(data.signature, { x: 600, y: 100, size: 14, font: font });
  }
  
  // 下载PDF
  const pdfBytes = await pdfDoc.save();
  const blob = new Blob([pdfBytes], { type: 'application/pdf' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `certificate-${data.recipientName}-${Date.now()}.pdf`;
  link.click();
  URL.revokeObjectURL(url);
}
```

## 📊 数据结构速查

### 基础类型
```typescript
interface CertificateData {
  recipientName: string;  // 获奖者姓名
  date: string;          // 日期
  signature: string;     // 签名
  details: string;       // 详细信息
}

interface CertificateTemplate {
  id: string;
  displayName: string;
  category: 'achievement' | 'completion' | 'participation' | 'excellence';
  orientation: 'landscape' | 'portrait';
  backgroundImage?: string;
  style: {
    colors: { primary: string; secondary: string; background: string; text: string; };
    fonts: { title: FontConfig; name: FontConfig; body: FontConfig; signature: FontConfig; };
  };
  constraints: { nameMaxLength: number; detailsMaxLength: number; /* ... */ };
}
```

## 🚀 30分钟快速部署

### 步骤1: 安装依赖 (2分钟)
```bash
npm install pdf-lib lucide-react @radix-ui/react-toast
```

### 步骤2: 创建目录结构 (1分钟)
```bash
mkdir -p src/{types,lib,components/certificate,app/certificate-templates/[category]}
mkdir -p public/templates
```

### 步骤3: 复制核心文件 (10分钟)
按照上面的代码片段创建8个核心文件

### 步骤4: 添加模板数据 (5分钟)
```typescript
// src/lib/certificate-templates.ts
export const CERTIFICATE_TEMPLATES = [
  {
    id: 'achievement-1',
    displayName: 'Classic Achievement',
    category: 'achievement',
    orientation: 'landscape',
    backgroundImage: '/templates/achievement-1.png',
    // ... 其他配置
  }
];
```

### 步骤5: 配置路由 (2分钟)
创建动态路由文件并配置模板管理器

### 步骤6: 测试功能 (10分钟)
1. 访问 `/certificate-templates/achievement-certificates/`
2. 测试表单输入
3. 验证预览更新
4. 测试PDF下载

## ⚠️ 常见问题

### PDF生成失败
- 检查 pdf-lib 版本兼容性
- 确认字体加载正确
- 验证坐标计算

### 预览不更新
- 检查 formData 状态传递
- 确认 useCallback 依赖项
- 验证条件渲染逻辑

### 路由404错误
- 确认动态路由文件位置
- 检查 TemplateManager 方法
- 验证分类slug映射

### 样式问题
- 确认 Tailwind CSS 配置
- 检查响应式断点
- 验证字体加载

## 📞 技术支持

如需详细实现说明，请参考完整的迁移指南：
`docs/ACHIEVEMENT_CERTIFICATES_MIGRATION_GUIDE.md`
