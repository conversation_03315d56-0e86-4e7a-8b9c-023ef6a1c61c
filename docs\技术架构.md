# 证书生成器 - 技术架构设计

## 1. 技术栈选择

### 1.1 前端框架
**Next.js 14 (App Router)**
- **选择理由**：
  - 服务端渲染(SSR)提升SEO和首屏加载速度
  - App Router提供更好的路由管理
  - 内置优化功能（图片、字体、代码分割）
  - Vercel部署优化
  - TypeScript原生支持

### 1.2 样式框架
**TailwindCSS**
- **选择理由**：
  - 原子化CSS，开发效率高
  - 内置响应式设计支持
  - 优秀的暗色模式支持
  - 与Next.js完美集成
  - 生产环境自动优化

### 1.3 UI组件库
**Shadcn/ui**
- **选择理由**：
  - 基于Radix UI，无障碍访问友好
  - 完全可定制的组件
  - TypeScript支持
  - 与TailwindCSS深度集成
  - 现代化的设计风格

### 1.4 PDF生成方案
**方案对比**：

| 方案 | 优点 | 缺点 | 适用场景 |
|------|------|------|----------|
| jsPDF + html2canvas | 纯前端，无服务器成本 | 英文支持有限，质量一般 | 简单证书 |
| Puppeteer | 高质量PDF，完美还原 | 需要服务器，成本较高 | 复杂布局 |
| PDF-lib | 轻量级，性能好 | 需要手动绘制 | 简单模板 |

**最终选择**：PDF-lib + Canvas API
- 前端生成，无服务器成本
- 支持英文字体优化
- 可控制的高质量输出
- 有利于Core Web Vitals优化

### 1.5 SEO技术架构
**Next.js SEO功能**：
- App Router的内置SEO支持
- 自动生成sitemap.xml
- 动态meta标签生成
- 结构化数据集成
- 图片优化和懒加载
- Core Web Vitals监控

## 2. 项目结构设计

```
certificate-maker/
├── app/                          # Next.js App Router
│   ├── globals.css              # 全局样式
│   ├── layout.tsx               # 根布局
│   ├── page.tsx                 # 首页 - 所有分类展示
│   ├── [category]/              # 动态分类路由
│   │   ├── page.tsx             # 分类页面
│   │   └── [template]/          # 动态模板路由
│   │       └── page.tsx         # 模板详情页
│   ├── sitemap.ts               # 动态sitemap生成
│   └── api/                     # API路由
│       ├── templates/
│       │   ├── route.ts         # 获取所有模板
│       │   └── [category]/
│       │       └── route.ts     # 获取分类模板
│       └── generate/
│           └── route.ts         # PDF生成API
├── components/                   # React组件
│   ├── ui/                      # Shadcn/ui基础组件
│   ├── certificate/             # 证书相关组件
│   │   ├── TemplateSelector.tsx # 模板选择器
│   │   ├── CertificateForm.tsx  # 证书表单
│   │   ├── CertificatePreview.tsx # 证书预览
│   │   ├── CategoryNavigation.tsx # 分类导航
│   │   └── templates/           # 分类模板组件
│   │       ├── AchievementTemplates.tsx
│   │       ├── CompletionTemplates.tsx
│   │       └── ParticipationTemplates.tsx
│   ├── layout/                  # 布局组件
│   │   ├── Breadcrumb.tsx       # 面包屑导航
│   │   └── CategoryFilter.tsx   # 分类筛选
│   └── common/                  # 通用组件
├── lib/                         # 工具库
│   ├── utils.ts                 # 通用工具函数
│   ├── pdf-generator.ts         # PDF生成逻辑
│   ├── certificate-templates.ts # 证书模板定义
│   ├── template-manager.ts      # 模板管理器
│   ├── recommendation-engine.ts # 推荐引擎
│   └── validations.ts           # 表单验证
├── types/                       # TypeScript类型定义
├── public/                      # 静态资源
│   ├── fonts/                   # 字体文件
│   ├── images/                  # 图片资源
│   └── templates/               # 模板资源
│       ├── achievement/         # 成就证书模板
│       ├── completion/          # 完成证书模板
│       └── participation/       # 参与证书模板
├── docs/                        # 项目文档
│   ├── 多类别模板架构设计.md    # 多类别架构文档
│   └── 模板配置规范.md          # 模板配置规范
└── tests/                       # 测试文件
```

## 3. 核心模块设计

### 3.1 多类别证书模板系统

```typescript
// types/certificate.ts
export enum CertificateCategory {
  ACHIEVEMENT = 'achievement',
  COMPLETION = 'completion',
  PARTICIPATION = 'participation',
  EXCELLENCE = 'excellence',
  CUSTOM = 'custom'
}

interface CertificateTemplate {
  // 基础信息
  id: string;
  name: string;
  displayName: string;
  description: string;

  // 分类信息
  category: CertificateCategory;
  tags: string[];

  // 视觉属性
  preview: string;
  backgroundImage?: string;
  orientation: 'portrait' | 'landscape';
  aspectRatio: number;

  // SEO属性
  seoTitle: string;
  seoDescription: string;
  seoKeywords: string[];

  style: {
    background: string;
    border: string;
    colors: {
      primary: string;
      secondary: string;
      text: string;
    };
    fonts: {
      title: FontConfig;
      body: FontConfig;
      signature: FontConfig;
    };
  };
  layout: {
    title: FixedPosition;
    name: FixedPosition;
    date: FixedPosition;
    signature: FixedPosition;
    details: FixedPosition;
  };
  constraints: {
    nameMaxLength: number;
    dateMaxLength: number;
    signatureMaxLength: number;
    detailsMaxLength: number;
  };
}

// 模板分类配置
interface TemplateCategory {
  id: CertificateCategory;
  name: string;
  displayName: string;
  description: string;
  seoKeywords: string[];
  urlSlug: string;
  metaTitle: string;
  metaDescription: string;
  templates: CertificateTemplate[];
}

interface FixedPosition {
  x: number;
  y: number;
  width: number;
  height: number;
  align: 'left' | 'center' | 'right';
  fontSize: number;
  fontFamily: string;
  color: string;
  // Fixed positioning - no dynamic adjustment
}
```

### 3.2 表单数据管理 (Mobile-First)

```typescript
// types/form.ts
interface CertificateData {
  templateId: string;
  recipientName: string;
  date: string;
  signature: string;
  details: string;
}

// Mobile-optimized validation with character limits
const certificateSchema = z.object({
  templateId: z.string().min(1, "Please select a certificate template"),
  recipientName: z.string()
    .min(1, "Please enter recipient name")
    .max(50, "Name must be 50 characters or less"),
  date: z.string()
    .min(1, "Please select a date")
    .max(20, "Date must be 20 characters or less"),
  signature: z.string()
    .min(1, "Please enter signature")
    .max(30, "Signature must be 30 characters or less"),
  details: z.string()
    .min(10, "Please enter details (at least 10 characters)")
    .max(200, "Details must be 200 characters or less")
});

// Mobile-specific form configuration
interface MobileFormConfig {
  inputType: 'text' | 'date' | 'textarea';
  placeholder: string;
  maxLength: number;
  showCharacterCount: boolean;
  mobileKeyboard: 'default' | 'numeric' | 'email';
}
```

### 3.3 PDF生成引擎

```typescript
// lib/pdf-generator.ts
class PDFGenerator {
  private template: CertificateTemplate;
  private data: CertificateData;

  constructor(template: CertificateTemplate, data: CertificateData) {
    this.template = template;
    this.data = data;
  }

  async generate(): Promise<Uint8Array> {
    const pdfDoc = await PDFDocument.create();
    const page = pdfDoc.addPage([595.28, 841.89]); // A4尺寸
    
    // 加载字体
    const font = await this.loadFont();
    
    // 绘制背景
    await this.drawBackground(page);
    
    // 绘制文本内容
    this.drawText(page, font);
    
    return await pdfDoc.save();
  }

  private async loadFont(): Promise<PDFFont> {
    // 加载中文字体支持
  }

  private async drawBackground(page: PDFPage): Promise<void> {
    // 绘制证书背景和装饰
  }

  private drawText(page: PDFPage, font: PDFFont): void {
    // 绘制证书文本内容
  }
}
```

## 4. 状态管理策略

### 4.1 本地状态管理
- **React useState**：组件内部状态
- **React Hook Form**：表单状态管理
- **Context API**：跨组件状态共享

### 4.2 状态结构设计

```typescript
// contexts/CertificateContext.tsx
interface CertificateContextType {
  // 当前选中的模板
  selectedTemplate: CertificateTemplate | null;
  setSelectedTemplate: (template: CertificateTemplate) => void;
  
  // 表单数据
  formData: CertificateData;
  updateFormData: (data: Partial<CertificateData>) => void;
  
  // UI状态
  isGenerating: boolean;
  setIsGenerating: (generating: boolean) => void;
  
  // 预览状态
  previewMode: 'desktop' | 'mobile';
  setPreviewMode: (mode: 'desktop' | 'mobile') => void;
}
```

## 5. 性能优化策略

### 5.1 代码分割
```typescript
// 动态导入PDF生成器
const PDFGenerator = dynamic(() => import('@/lib/pdf-generator'), {
  loading: () => <LoadingSpinner />,
  ssr: false
});

// 模板懒加载
const CertificateTemplate = lazy(() => import('./CertificateTemplate'));
```

### 5.2 资源优化
- **字体优化**：使用font-display: swap
- **图片优化**：Next.js Image组件
- **CSS优化**：TailwindCSS purge
- **Bundle优化**：webpack-bundle-analyzer

### 5.3 缓存策略
```typescript
// 模板缓存
const templateCache = new Map<string, CertificateTemplate>();

// 字体缓存
const fontCache = new Map<string, ArrayBuffer>();

// 预览缓存
const previewCache = new Map<string, string>();
```

## 6. 安全考虑

### 6.1 输入验证
- 客户端表单验证（Zod）
- XSS防护（DOMPurify）
- 文件大小限制
- 字符长度限制

### 6.2 内容安全
```typescript
// 内容安全策略
const csp = {
  'default-src': ["'self'"],
  'script-src': ["'self'", "'unsafe-inline'"],
  'style-src': ["'self'", "'unsafe-inline'", 'fonts.googleapis.com'],
  'font-src': ["'self'", 'fonts.gstatic.com'],
  'img-src': ["'self'", 'data:', 'https:']
};
```

## 7. SEO技术实现

### 7.1 元数据管理
```typescript
// app/layout.tsx - 全局SEO配置
export const metadata: Metadata = {
  title: {
    default: 'Free Certificate Maker | Create Professional PDF Certificates Online',
    template: '%s | Certificate Maker'
  },
  description: 'Create beautiful, professional certificates online for free. Choose from 4 elegant templates, customize with your details, and download high-quality PDF certificates instantly.',
  keywords: ['certificate maker', 'free certificate generator', 'online certificate creator', 'PDF certificate'],
  authors: [{ name: 'Certificate Maker Team' }],
  creator: 'Certificate Maker',
  publisher: 'Certificate Maker',
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://certificatemaker.com',
    siteName: 'Certificate Maker',
    title: 'Free Certificate Maker - Create Professional Certificates Online',
    description: 'Generate beautiful PDF certificates instantly with our free online tool.',
    images: [
      {
        url: '/images/og-certificate-maker.jpg',
        width: 1200,
        height: 630,
        alt: 'Certificate Maker - Free Online Certificate Generator',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Free Certificate Maker - Create Professional Certificates',
    description: 'Generate beautiful PDF certificates instantly with our free online tool.',
    images: ['/images/twitter-certificate-maker.jpg'],
  },
};
```

### 7.2 结构化数据实现
```typescript
// lib/structured-data.ts
export const webApplicationSchema = {
  '@context': 'https://schema.org',
  '@type': 'WebApplication',
  name: 'Certificate Maker',
  description: 'Free online certificate generator for creating professional PDF certificates',
  url: 'https://certificatemaker.com',
  applicationCategory: 'DesignApplication',
  operatingSystem: 'Web Browser',
  offers: {
    '@type': 'Offer',
    price: '0',
    priceCurrency: 'USD'
  },
  featureList: [
    '4 Professional Certificate Templates',
    'Real-time Preview',
    'High-Quality PDF Generation',
    'Mobile-Friendly Design',
    'No Registration Required'
  ]
};
```

### 7.3 性能监控和SEO分析
```typescript
// lib/analytics.ts
// Core Web Vitals监控
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

function sendToAnalytics(metric: any) {
  gtag('event', metric.name, {
    event_category: 'Web Vitals',
    event_label: metric.id,
    value: Math.round(metric.name === 'CLS' ? metric.value * 1000 : metric.value),
    non_interaction: true,
  });
}

getCLS(sendToAnalytics);
getFID(sendToAnalytics);
getFCP(sendToAnalytics);
getLCP(sendToAnalytics);
getTTFB(sendToAnalytics);

// SEO相关事件追踪
export const trackSEOEvents = {
  certificateGenerated: (templateType: string, generationTime: number) => {
    gtag('event', 'certificate_generated', {
      template_type: templateType,
      generation_time: generationTime,
      event_category: 'Certificate Creation'
    });
  },
  templateSelected: (templateName: string) => {
    gtag('event', 'template_selected', {
      template_name: templateName,
      event_category: 'User Interaction'
    });
  },
  pdfDownloaded: (certificateType: string) => {
    gtag('event', 'pdf_downloaded', {
      certificate_type: certificateType,
      event_category: 'Conversion'
    });
  }
};
```

## 8. 部署架构

### 8.1 Vercel部署配置
```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/fonts/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "public, max-age=31536000, immutable"
        }
      ]
    }
  ]
}
```

### 8.2 环境配置
```typescript
// 环境变量
interface Config {
  NODE_ENV: 'development' | 'production';
  NEXT_PUBLIC_GA_ID?: string;
  NEXT_PUBLIC_SENTRY_DSN?: string;
  FONT_CDN_URL: string;
}
```

## 9. 测试策略

### 9.1 测试金字塔
- **单元测试**：Jest + React Testing Library
- **集成测试**：Playwright
- **E2E测试**：Cypress

### 9.2 测试覆盖
```typescript
// 关键测试用例
describe('Certificate Generator', () => {
  test('should generate PDF with correct content', async () => {
    // PDF生成测试
  });
  
  test('should validate form inputs', () => {
    // 表单验证测试
  });
  
  test('should be responsive on mobile', () => {
    // 响应式测试
  });
});
```

## 10. 扩展性考虑

### 10.1 模块化设计
- 插件化模板系统
- 可扩展的PDF生成器
- 主题系统支持

### 10.2 国际化准备
```typescript
// i18n配置
const i18nConfig = {
  locales: ['zh-CN', 'en-US'],
  defaultLocale: 'zh-CN',
  domains: [
    {
      domain: 'certificate-maker.com',
      defaultLocale: 'en-US'
    },
    {
      domain: 'certificate-maker.cn',
      defaultLocale: 'zh-CN'
    }
  ]
};
```

这个技术架构设计确保了项目的可维护性、可扩展性和高性能，同时满足了用户体验和业务需求。
