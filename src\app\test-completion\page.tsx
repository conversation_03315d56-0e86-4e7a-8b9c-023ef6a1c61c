'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import CertificatePreview from '@/components/certificate/CertificatePreview';
import { CertificateData } from '@/types/certificate';
import { CERTIFICATE_TEMPLATES } from '@/lib/certificate-templates';
import { generateCertificatePDF } from '@/lib/pdf-generator';
import { useToast } from '@/hooks/use-toast';

export default function TestCompletionPage() {
  const { toast } = useToast();
  
  // 获取completion-template-1
  const template = CERTIFICATE_TEMPLATES.find(t => t.id === 'completion-template-1');
  
  const [formData, setFormData] = useState<CertificateData>({
    templateId: 'completion-template-1',
    recipientName: '张三',
    date: '2024年1月15日',
    signature: '李老师',
    details: '恭喜您成功完成了《Web开发基础课程》的学习，表现优异，特此颁发此证书以资鼓励。',
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [showCoordinates, setShowCoordinates] = useState(false);

  const handleGeneratePDF = async () => {
    if (!template) return;
    
    setIsGenerating(true);
    try {
      await generateCertificatePDF(template, formData);
      toast({
        title: "PDF生成成功",
        description: "证书已生成并下载",
        variant: "default",
      });
    } catch (error) {
      console.error('PDF generation error:', error);
      toast({
        title: "PDF生成失败",
        description: "请稍后重试",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  if (!template) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">模板未找到</h1>
          <p>completion-template-1 模板不存在</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            Completion Template 测试页面
          </h1>
          <p className="text-gray-600">
            测试预览图与PDF生成位置一致性以及Dancing Script字体效果
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 左侧：表单 */}
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">证书信息</h2>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    收件人姓名
                  </label>
                  <input
                    type="text"
                    value={formData.recipientName}
                    onChange={(e) => setFormData(prev => ({ ...prev, recipientName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    日期
                  </label>
                  <input
                    type="text"
                    value={formData.date}
                    onChange={(e) => setFormData(prev => ({ ...prev, date: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    签名
                  </label>
                  <input
                    type="text"
                    value={formData.signature}
                    onChange={(e) => setFormData(prev => ({ ...prev, signature: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    详细信息
                  </label>
                  <textarea
                    value={formData.details}
                    onChange={(e) => setFormData(prev => ({ ...prev, details: e.target.value }))}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="mt-6 space-y-3">
                <Button
                  onClick={handleGeneratePDF}
                  disabled={isGenerating}
                  className="w-full"
                >
                  {isGenerating ? '生成中...' : '生成PDF'}
                </Button>

                <Button
                  onClick={() => setShowCoordinates(!showCoordinates)}
                  variant="outline"
                  className="w-full"
                >
                  {showCoordinates ? '隐藏坐标' : '显示坐标'}
                </Button>
              </div>
            </div>

            {/* 模板信息 */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold mb-2">模板信息</h3>
              <div className="text-sm text-gray-600 space-y-1">
                <p><strong>ID:</strong> {template.id}</p>
                <p><strong>方向:</strong> {template.orientation}</p>
                <p><strong>宽高比:</strong> {template.aspectRatio}</p>
                <p><strong>字体:</strong> {template.style.fonts.name.family}</p>
              </div>
            </div>

            {/* 坐标信息 */}
            {showCoordinates && (
              <div className="bg-blue-50 rounded-lg p-4">
                <h3 className="font-semibold mb-2">坐标信息</h3>
                <div className="text-xs text-gray-600 space-y-2">
                  <div>
                    <strong>姓名:</strong> x:{template.layout.name.x}, y:{template.layout.name.y},
                    w:{template.layout.name.width}, h:{template.layout.name.height}
                  </div>
                  <div>
                    <strong>详情:</strong> x:{template.layout.details.x}, y:{template.layout.details.y},
                    w:{template.layout.details.width}, h:{template.layout.details.height}
                  </div>
                  <div>
                    <strong>日期:</strong> x:{template.layout.date.x}, y:{template.layout.date.y},
                    w:{template.layout.date.width}, h:{template.layout.date.height}
                  </div>
                  <div>
                    <strong>签名:</strong> x:{template.layout.signature.x}, y:{template.layout.signature.y},
                    w:{template.layout.signature.width}, h:{template.layout.signature.height}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* 右侧：预览 */}
          <div className="lg:sticky lg:top-8">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold mb-4">实时预览</h2>
              <CertificatePreview
                template={template}
                formData={formData}
                showCoordinates={showCoordinates}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
