'use client';

import { useEffect } from 'react';

// 声明全局gtag函数
declare global {
  function gtag(...args: any[]): void;
}

interface PerformanceMetrics {
  lcp?: number;
  fid?: number;
  cls?: number;
  fcp?: number;
  ttfb?: number;
}

export default function PerformanceMonitor() {
  useEffect(() => {
    // 只在生产环境中运行性能监控
    if (process.env.NODE_ENV !== 'production') {
      return;
    }

    const metrics: PerformanceMetrics = {};

    // 监控 Core Web Vitals
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        switch (entry.entryType) {
          case 'largest-contentful-paint':
            metrics.lcp = entry.startTime;
            break;
          case 'first-input':
            metrics.fid = (entry as any).processingStart - entry.startTime;
            break;
          case 'layout-shift':
            if (!(entry as any).hadRecentInput) {
              metrics.cls = (metrics.cls || 0) + (entry as any).value;
            }
            break;
        }
      }
    });

    // 观察性能指标
    try {
      observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    } catch (e) {
      // 某些浏览器可能不支持所有指标
      console.warn('Performance monitoring not fully supported:', e);
    }

    // 监控导航时间
    const measureNavigationTiming = () => {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigation) {
        metrics.fcp = navigation.responseStart - navigation.fetchStart;
        metrics.ttfb = navigation.responseStart - navigation.requestStart;
      }
    };

    // 页面加载完成后测量
    if (document.readyState === 'complete') {
      measureNavigationTiming();
    } else {
      window.addEventListener('load', measureNavigationTiming);
    }

    // 页面卸载时发送数据
    const sendMetrics = () => {
      if (Object.keys(metrics).length > 0) {
        // 发送到分析服务
        if (typeof gtag !== 'undefined') {
          // Google Analytics 4
          gtag('event', 'web_vitals', {
            lcp: metrics.lcp,
            fid: metrics.fid,
            cls: metrics.cls,
            fcp: metrics.fcp,
            ttfb: metrics.ttfb,
          });
        }

        // 也可以发送到自定义分析端点
        if (navigator.sendBeacon) {
          navigator.sendBeacon('/api/analytics/performance', JSON.stringify(metrics));
        }
      }
    };

    // 页面卸载时发送指标
    window.addEventListener('beforeunload', sendMetrics);
    
    // 页面隐藏时也发送指标（移动端）
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        sendMetrics();
      }
    });

    return () => {
      observer.disconnect();
      window.removeEventListener('load', measureNavigationTiming);
      window.removeEventListener('beforeunload', sendMetrics);
    };
  }, []);

  return null; // 这是一个无UI组件
}

// 手动测量自定义性能指标的工具函数
export const measurePerformance = {
  // 测量PDF生成时间
  measurePDFGeneration: (startTime: number, endTime: number) => {
    const duration = endTime - startTime;
    
    if (typeof gtag !== 'undefined') {
      gtag('event', 'pdf_generation_time', {
        event_category: 'Performance',
        event_label: 'PDF Generation',
        value: Math.round(duration),
      });
    }

    // 记录到控制台（开发环境）
    if (process.env.NODE_ENV === 'development') {
      console.log(`PDF Generation Time: ${duration}ms`);
    }

    return duration;
  },

  // 测量表单验证时间
  measureFormValidation: (startTime: number, endTime: number) => {
    const duration = endTime - startTime;
    
    if (typeof gtag !== 'undefined') {
      gtag('event', 'form_validation_time', {
        event_category: 'Performance',
        event_label: 'Form Validation',
        value: Math.round(duration),
      });
    }

    return duration;
  },

  // 测量预览渲染时间
  measurePreviewRender: (startTime: number, endTime: number) => {
    const duration = endTime - startTime;
    
    if (typeof gtag !== 'undefined') {
      gtag('event', 'preview_render_time', {
        event_category: 'Performance',
        event_label: 'Preview Render',
        value: Math.round(duration),
      });
    }

    return duration;
  },

  // 标记性能时间点
  mark: (name: string) => {
    if (performance.mark) {
      performance.mark(name);
    }
  },

  // 测量两个标记之间的时间
  measure: (name: string, startMark: string, endMark: string) => {
    if (performance.measure) {
      performance.measure(name, startMark, endMark);
      const measure = performance.getEntriesByName(name)[0];
      return measure?.duration || 0;
    }
    return 0;
  },
};

// 性能预算检查
export const checkPerformanceBudget = () => {
  const budgets = {
    lcp: 2500, // 2.5秒
    fid: 100,  // 100毫秒
    cls: 0.1,  // 0.1
    fcp: 1800, // 1.8秒
    ttfb: 600, // 600毫秒
  };

  const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
  const results = {
    passed: 0,
    failed: 0,
    details: {} as Record<string, { value: number; budget: number; passed: boolean }>,
  };

  if (navigation) {
    const ttfb = navigation.responseStart - navigation.requestStart;
    const fcp = navigation.responseStart - navigation.fetchStart;

    results.details.ttfb = {
      value: ttfb,
      budget: budgets.ttfb,
      passed: ttfb <= budgets.ttfb,
    };

    results.details.fcp = {
      value: fcp,
      budget: budgets.fcp,
      passed: fcp <= budgets.fcp,
    };

    if (results.details.ttfb.passed) results.passed++;
    else results.failed++;

    if (results.details.fcp.passed) results.passed++;
    else results.failed++;
  }

  return results;
};
