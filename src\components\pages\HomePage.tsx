'use client';

import Link from 'next/link';
import { TemplateManager } from '@/lib/template-manager';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import PageLayout from '@/components/layout/PageLayout';
import CategoryPreviewGrid from '@/components/certificate/CategoryPreviewGrid';
import { ArrowRight, Zap, Download, Sparkles } from 'lucide-react';

export default function HomePage() {
  // 获取所有分类数据
  const categories = TemplateManager.getAllCategories();

  return (
    <PageLayout className="bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="container mx-auto px-4 py-16">
        {/* Hero Section */}
        <section className="text-center mb-16 sm:mb-20 px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              Create Professional
              <span className="text-blue-600 block">Certificates Online</span>
            </h1>
            <p className="text-base sm:text-lg md:text-xl text-gray-600 mb-8 sm:mb-10 leading-relaxed max-w-3xl mx-auto">
              Design beautiful, professional certificates in minutes. Choose from our elegant templates, customize with your details, and download high-quality PDFs instantly - completely free.
            </p>

            {/* Main CTA */}
            <div className="mb-8 sm:mb-12">
              <Link href="/certificate-templates/">
                <Button size="lg" className="text-base sm:text-lg px-6 sm:px-12 py-4 sm:py-6 text-white bg-blue-600 hover:bg-blue-700 w-full sm:w-auto min-h-[48px] sm:min-h-auto">
                  <span className="sm:hidden">Get Started</span>
                  <span className="hidden sm:inline">Get Started - Browse Templates</span>
                  <ArrowRight className="w-5 h-5 sm:w-6 sm:h-6 ml-2 sm:ml-3" />
                </Button>
              </Link>
            </div>

            {/* Quick Stats */}
            <div className="flex flex-col sm:flex-row justify-center items-center space-y-3 sm:space-y-0 sm:space-x-6 md:space-x-8 text-xs sm:text-sm text-gray-500">
              <div className="flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs sm:text-base px-3 sm:px-4 py-1 sm:py-2">
                  {categories.reduce((total, cat) => total + (cat.templateCount || 0), 0)} Templates
                </Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs sm:text-base px-3 sm:px-4 py-1 sm:py-2">{categories.length} Categories</Badge>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant="outline" className="text-xs sm:text-base px-3 sm:px-4 py-1 sm:py-2">100% Free</Badge>
              </div>
            </div>
          </div>
        </section>

        {/* Categories Preview */}
        <section className="mb-16 sm:mb-20">
          <CategoryPreviewGrid
            categories={categories}
            title="Choose Your Certificate Type"
            subtitle="Each category is designed for specific use cases with professional templates that make your certificates stand out."
          />

          <div className="text-center mt-8 sm:mt-12 px-4">
            <Link href="/certificate-templates/">
              <Button variant="outline" size="lg" className="text-base sm:text-lg px-6 sm:px-8 py-3 sm:py-4 w-full sm:w-auto min-h-[48px] sm:min-h-auto">
                View All Templates
                <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5 ml-2" />
              </Button>
            </Link>
          </div>
        </section>

        {/* Features Section */}
        <section className="mb-12 sm:mb-16 px-4">
          <div className="text-center mb-8 sm:mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4 sm:mb-6 leading-tight">
              Why Choose Our Certificate Maker?
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 sm:gap-8">
            <Card className="text-center border-2 hover:border-blue-200 transition-colors">
              <CardHeader className="pb-3 sm:pb-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                  <Zap className="w-8 h-8 sm:w-10 sm:h-10 text-blue-600" />
                </div>
                <CardTitle className="text-lg sm:text-xl md:text-2xl">Lightning Fast</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm sm:text-base md:text-lg leading-relaxed">
                  Create professional certificates in under 2 minutes. No design skills required.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center border-2 hover:border-green-200 transition-colors">
              <CardHeader className="pb-3 sm:pb-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                  <Download className="w-8 h-8 sm:w-10 sm:h-10 text-green-600" />
                </div>
                <CardTitle className="text-lg sm:text-xl md:text-2xl">High Quality Output</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm sm:text-base md:text-lg leading-relaxed">
                  Download print-ready PDFs with crisp text and professional formatting.
                </CardDescription>
              </CardContent>
            </Card>

            <Card className="text-center border-2 hover:border-purple-200 transition-colors">
              <CardHeader className="pb-3 sm:pb-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4 sm:mb-6">
                  <Sparkles className="w-8 h-8 sm:w-10 sm:h-10 text-purple-600" />
                </div>
                <CardTitle className="text-lg sm:text-xl md:text-2xl">Professional Templates</CardTitle>
              </CardHeader>
              <CardContent className="pt-0">
                <CardDescription className="text-sm sm:text-base md:text-lg leading-relaxed">
                  Carefully designed templates that look impressive and professional.
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </section>
      </div>
    </PageLayout>
  );
}
