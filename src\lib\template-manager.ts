import { CertificateTemplate, CertificateCategory, TemplateCategory } from '@/types/certificate';
import { CERTIFICATE_TEMPLATES } from './certificate-templates';

/**
 * 模板管理工具类
 * 提供模板分类、查询、管理等功能
 */
export class TemplateManager {
  /**
   * 根据分类获取模板列表
   */
  static getTemplatesByCategory(category: CertificateCategory): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES.filter(template => template.category === category);
  }

  /**
   * 根据模板ID获取模板
   */
  static getTemplateById(id: string): CertificateTemplate | undefined {
    return CERTIFICATE_TEMPLATES.find(template => template.id === id);
  }

  /**
   * 根据模板名称获取模板
   */
  static getTemplateByName(name: string): CertificateTemplate | undefined {
    return CERTIFICATE_TEMPLATES.find(template => template.name === name);
  }

  /**
   * 获取所有模板
   */
  static getAllTemplates(): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES;
  }

  /**
   * 根据方向获取模板
   */
  static getTemplatesByOrientation(orientation: 'portrait' | 'landscape'): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES.filter(template => template.orientation === orientation);
  }

  /**
   * 根据标签搜索模板
   */
  static searchTemplatesByTags(tags: string[]): CertificateTemplate[] {
    return CERTIFICATE_TEMPLATES.filter(template => 
      tags.some(tag => template.tags.includes(tag))
    );
  }

  /**
   * 获取分类URL slug对应的分类
   */
  static getCategoryBySlug(slug: string): CertificateCategory | undefined {
    const categoryMap: Record<string, CertificateCategory> = {
      'achievement': CertificateCategory.ACHIEVEMENT,
      'completion': CertificateCategory.COMPLETION,
      'participation': CertificateCategory.PARTICIPATION,
      'excellence': CertificateCategory.EXCELLENCE,
      'custom': CertificateCategory.CUSTOM
    };
    return categoryMap[slug];
  }

  /**
   * 获取分类对应的URL slug
   */
  static getSlugByCategory(category: CertificateCategory): string {
    const slugMap: Record<CertificateCategory, string> = {
      [CertificateCategory.ACHIEVEMENT]: 'achievement',
      [CertificateCategory.COMPLETION]: 'completion',
      [CertificateCategory.PARTICIPATION]: 'participation',
      [CertificateCategory.EXCELLENCE]: 'excellence',
      [CertificateCategory.CUSTOM]: 'custom'
    };
    return slugMap[category];
  }

  /**
   * 获取分类的默认预览图片
   */
  static getCategoryPreviewImage(category: CertificateCategory): string {
    const templates = this.getTemplatesByCategory(category);
    if (templates.length > 0) {
      // 使用该分类下第一个模板的预览图片作为分类预览
      return templates[0].preview || templates[0].backgroundImage || '/templates/default-preview.jpg';
    }
    return '/templates/default-preview.jpg';
  }

  /**
   * 获取分类的模板数量
   */
  static getCategoryTemplateCount(category: CertificateCategory): number {
    return this.getTemplatesByCategory(category).length;
  }

  /**
   * 获取所有分类配置
   */
  static getAllCategories(): TemplateCategory[] {
    const categories: TemplateCategory[] = [
      {
        id: CertificateCategory.ACHIEVEMENT,
        name: 'achievement',
        displayName: 'Achievement Certificate Templates',
        description: 'Professional achievement certificate templates for awards and recognition',
        seoKeywords: ['achievement certificate template', 'award certificate', 'recognition certificate', 'professional achievement'],
        urlSlug: 'achievement-certificates',
        metaTitle: 'Achievement Certificate Templates | Free Professional Awards - Certificate Maker',
        metaDescription: 'Create stunning achievement certificates with our professional templates. Perfect for employee recognition, academic awards, and business achievements. Download instantly as PDF.',
        templates: this.getTemplatesByCategory(CertificateCategory.ACHIEVEMENT),
        defaultSize: 'landscape' as const,
        defaultTemplate: 'achievement-template-1',
        previewImage: this.getCategoryPreviewImage(CertificateCategory.ACHIEVEMENT),
        templateCount: this.getCategoryTemplateCount(CertificateCategory.ACHIEVEMENT)
      },
      {
        id: CertificateCategory.COMPLETION,
        name: 'completion',
        displayName: 'Completion Certificate Templates',
        description: 'Course and training completion certificate templates',
        seoKeywords: ['completion certificate template', 'course completion', 'training certificate', 'educational certificate'],
        urlSlug: 'completion-certificates',
        metaTitle: 'Completion Certificate Templates | Course & Training Certificates - Certificate Maker',
        metaDescription: 'Generate professional completion certificates for courses, training programs, and educational achievements. Free templates with instant PDF download.',
        templates: this.getTemplatesByCategory(CertificateCategory.COMPLETION),
        defaultSize: 'landscape' as const,
        defaultTemplate: 'completion-template-1',
        previewImage: this.getCategoryPreviewImage(CertificateCategory.COMPLETION),
        templateCount: this.getCategoryTemplateCount(CertificateCategory.COMPLETION)
      },
      {
        id: CertificateCategory.PARTICIPATION,
        name: 'participation',
        displayName: 'Participation Certificate Templates',
        description: 'Event and workshop participation certificate templates',
        seoKeywords: ['participation certificate template', 'event participation', 'workshop certificate', 'seminar certificate'],
        urlSlug: 'participation-certificates',
        metaTitle: 'Participation Certificate Templates | Event & Workshop Certificates - Certificate Maker',
        metaDescription: 'Create participation certificates for events, workshops, seminars, and conferences. Professional templates with customizable designs.',
        templates: this.getTemplatesByCategory(CertificateCategory.PARTICIPATION),
        defaultSize: 'landscape' as const,
        defaultTemplate: 'participation-template-1',
        previewImage: this.getCategoryPreviewImage(CertificateCategory.PARTICIPATION),
        templateCount: this.getCategoryTemplateCount(CertificateCategory.PARTICIPATION)
      },
      {
        id: CertificateCategory.EXCELLENCE,
        name: 'excellence',
        displayName: 'Excellence Certificate Templates',
        description: 'Outstanding performance and excellence certificate templates',
        seoKeywords: ['excellence certificate template', 'outstanding performance', 'excellence award', 'merit certificate'],
        urlSlug: 'excellence-certificates',
        metaTitle: 'Excellence Certificate Templates | Outstanding Performance Awards - Certificate Maker',
        metaDescription: 'Recognize outstanding performance with our excellence certificate templates. Perfect for academic excellence, employee recognition, and merit awards.',
        templates: this.getTemplatesByCategory(CertificateCategory.EXCELLENCE),
        defaultSize: 'landscape' as const,
        defaultTemplate: 'excellence-template-1',
        previewImage: this.getCategoryPreviewImage(CertificateCategory.EXCELLENCE),
        templateCount: this.getCategoryTemplateCount(CertificateCategory.EXCELLENCE)
      }
    ];

    return categories;
  }

  /**
   * 根据URL slug获取分类配置
   */
  static getCategoryConfigBySlug(slug: string): TemplateCategory | undefined {
    return this.getAllCategories().find(category => category.urlSlug === slug);
  }



  /**
   * 生成sitemap条目
   */
  static generateSitemapEntries(): Array<{
    url: string;
    lastModified: Date;
    changeFrequency: 'weekly' | 'monthly';
    priority: number;
  }> {
    const entries: Array<{
      url: string;
      lastModified: Date;
      changeFrequency: 'weekly' | 'monthly';
      priority: number;
    }> = [];

    // 添加分类页面
    this.getAllCategories().forEach(category => {
      entries.push({
        url: `/certificate-templates/${category.urlSlug}/`,
        lastModified: new Date(),
        changeFrequency: 'weekly',
        priority: 0.8
      });

      // 添加模板页面
      category.templates.forEach(template => {
        entries.push({
          url: `/${category.urlSlug}/${template.name}/`,
          lastModified: new Date(),
          changeFrequency: 'monthly',
          priority: 0.6
        });
      });
    });

    return entries;
  }

  /**
   * 验证模板配置完整性
   */
  static validateTemplateConfiguration(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    CERTIFICATE_TEMPLATES.forEach((template, index) => {
      // 检查必需字段
      if (!template.id) errors.push(`Template ${index}: Missing id`);
      if (!template.name) errors.push(`Template ${index}: Missing name`);
      if (!template.displayName) errors.push(`Template ${index}: Missing displayName`);
      if (!template.category) errors.push(`Template ${index}: Missing category`);
      if (!template.seoTitle) errors.push(`Template ${index}: Missing seoTitle`);
      if (!template.seoDescription) errors.push(`Template ${index}: Missing seoDescription`);
      if (!template.seoKeywords || template.seoKeywords.length === 0) {
        errors.push(`Template ${index}: Missing or empty seoKeywords`);
      }

      // 检查标签
      if (!template.tags || template.tags.length === 0) {
        warnings.push(`Template ${index}: No tags defined`);
      }

      // 检查SEO关键词长度
      if (template.seoTitle && template.seoTitle.length > 60) {
        warnings.push(`Template ${index}: SEO title too long (${template.seoTitle.length} chars)`);
      }
      if (template.seoDescription && template.seoDescription.length > 160) {
        warnings.push(`Template ${index}: SEO description too long (${template.seoDescription.length} chars)`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
