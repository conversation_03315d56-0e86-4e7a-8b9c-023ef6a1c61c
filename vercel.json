{"buildCommand": "yarn build", "outputDirectory": ".next", "framework": "nextjs", "regions": ["iad1"], "functions": {"src/app/api/sitemap/route.ts": {"maxDuration": 10}, "src/app/api/robots/route.ts": {"maxDuration": 5}}, "headers": [{"source": "/fonts/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}]}], "redirects": [{"source": "/sitemap", "destination": "/sitemap.xml", "permanent": true}, {"source": "/robots", "destination": "/robots.txt", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}]}